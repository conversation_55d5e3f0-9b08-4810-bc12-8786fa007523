# 油品提取预约接口文档

## 接口概述

本接口用于创建油品提取预约申请，支持以下功能：
- 新增油品提取预约
- 查询预约详情
- 数据验证和审批状态管理

## 接口地址

- **基础路径**: `/exam/api/oil/reservation`
- **新增预约**: `POST /exam/api/oil/reservation/save`
- **查询详情**: `GET /exam/api/oil/reservation/detail/{id}`
- **测试接口**: `GET /exam/api/oil/reservation/test`

## 新增预约接口

### 请求方式
```
POST /exam/api/oil/reservation/save
```

### 请求参数

```json
{
  "scheduledTime": "2024-12-20 14:30:00",
  "oilDepot": "北京石油库",
  "oilType": "92#",
  "oilTonnage": 30.00,
  "licensePlate": "京A12345",
  "driverName": "张三",
  "driverIdCard": "110101199001011234",
  "driverPhone": "13800138000",
  "driverLicenseNumber": "110101199001011234",
  "licenseArchiveNumber": "A123456789",
  "tankCount": "双仓",
  "tank1LoadAmount": 15.00,
  "tank1OilType": "92#",
  "tank2LoadAmount": 15.00,
  "tank2OilType": "92#",
  "remark": "测试预约"
}
```

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scheduledTime | DateTime | 是 | 预定提油时间，格式：yyyy-MM-dd HH:mm:ss |
| oilDepot | String | 是 | 油库名称，最大100字符 |
| oilType | String | 是 | 油品种类，只能是：0#、92#、95# |
| oilTonnage | Decimal | 是 | 提油吨数，范围：0.01-999.99 |
| licensePlate | String | 是 | 车牌号，需符合中国车牌格式 |
| driverName | String | 是 | 司机姓名，最大50字符 |
| driverIdCard | String | 是 | 司机身份证号，18位身份证格式 |
| driverPhone | String | 是 | 司机手机号，11位手机号格式 |
| driverLicenseNumber | String | 是 | 司机驾驶证号，最大50字符 |
| licenseArchiveNumber | String | 是 | 驾驶证档案号，最大50字符 |
| tankCount | String | 是 | 车辆仓数，只能是：单仓、双仓、三仓 |
| tank1LoadAmount | Decimal | 否 | 第一仓装载量（吨），范围：0.00-999.99 |
| tank1OilType | String | 否 | 第一仓油品类型，只能是：0#、92#、95# |
| tank2LoadAmount | Decimal | 否 | 第二仓装载量（吨），范围：0.00-999.99 |
| tank2OilType | String | 否 | 第二仓油品类型，只能是：0#、92#、95# |
| tank3LoadAmount | Decimal | 否 | 第三仓装载量（吨），范围：0.00-999.99 |
| tank3OilType | String | 否 | 第三仓油品类型，只能是：0#、92#、95# |
| remark | String | 否 | 备注，最大500字符 |

### 各仓装载信息说明

各仓装载信息通过独立字段存储：

**字段说明：**
- `tank1LoadAmount` + `tank1OilType`：第一仓装载量和油品类型
- `tank2LoadAmount` + `tank2OilType`：第二仓装载量和油品类型
- `tank3LoadAmount` + `tank3OilType`：第三仓装载量和油品类型

**示例：**
- 单仓：只填写 `tank1LoadAmount` 和 `tank1OilType`
- 双仓：填写 `tank1LoadAmount`、`tank1OilType`、`tank2LoadAmount`、`tank2OilType`
- 三仓：填写所有6个字段

**验证规则：**
1. 装载量必须大于0（如果填写）
2. 有装载量时必须指定对应的油品类型
3. 油品类型只能是0#、92#、95#
4. 各仓装载量总和必须等于提油吨数
5. 实际使用的仓数必须与tankCount字段匹配

### 响应结果

**成功响应：**
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "1234567890123456789"
  }
}
```

**失败响应：**
```json
{
  "success": false,
  "code": 500,
  "message": "新增油品提取预约失败: 各仓装载量总和必须等于提油吨数",
  "data": null
}
```

## 查询详情接口

### 请求方式
```
GET /exam/api/oil/reservation/detail/{id}
```

### 路径参数
- `id`: 预约ID

### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "1234567890123456789",
    "scheduledTime": "2024-12-20 14:30:00",
    "oilDepot": "北京石油库",
    "oilType": "92#",
    "oilTonnage": 30.00,
    "licensePlate": "京A12345",
    "driverName": "张三",
    "driverIdCard": "110101199001011234",
    "driverPhone": "13800138000",
    "driverLicenseNumber": "110101199001011234",
    "licenseArchiveNumber": "A123456789",
    "tankCount": "双仓",
    "tank1LoadAmount": 15.00,
    "tank1OilType": "92#",
    "tank2LoadAmount": 15.00,
    "tank2OilType": "92#",
    "submitTime": "2024-12-19 10:30:00",
    "approvalStatus": 0,
    "approvalStatusDesc": "待审批",
    "reviewStatus": 0,
    "reviewStatusDesc": "待审核",
    "createTime": "2024-12-19 10:30:00",
    "createBy": "系统用户",
    "remark": "测试预约"
  }
}
```

## 状态说明

### 审批状态 (approvalStatus)

| 状态值 | 状态描述 | 说明 |
|--------|----------|------|
| 0 | 待审批 | 预约已提交，等待审批 |
| 1 | 审批通过 | 预约已通过审批 |
| 2 | 审批拒绝 | 预约被拒绝 |

### 审核状态 (reviewStatus)

| 状态值 | 状态描述 | 说明 |
|--------|----------|------|
| 0 | 待审核 | 预约已提交，等待审核 |
| 1 | 审核通过 | 预约已通过审核 |
| 2 | 审核拒绝 | 预约被拒绝 |

## 数据存储

数据存储在MongoDB中，集合名称为：`oil_reservation`

## 错误码说明

| 错误信息 | 说明 |
|----------|------|
| 预定提油时间不能是过去时间 | 预约时间必须是未来时间 |
| 各仓装载量总和必须等于提油吨数 | 装载信息验证失败 |
| 实际使用的仓数与车辆仓数不匹配 | 仓数验证失败 |
| 第X仓有装载量时必须指定油品类型 | 装载量和油品类型不匹配 |
| 第X仓油品类型只能是0#、92#、95# | 油品类型验证失败 |

## 使用示例

### cURL 示例

```bash
# 新增预约
curl -X POST "http://localhost:8080/exam/api/oil/reservation/save" \
  -H "Content-Type: application/json" \
  -d '{
    "scheduledTime": "2024-12-20 14:30:00",
    "oilDepot": "北京石油库",
    "oilType": "92#",
    "oilTonnage": 30.00,
    "licensePlate": "京A12345",
    "driverName": "张三",
    "driverIdCard": "110101199001011234",
    "driverPhone": "13800138000",
    "driverLicenseNumber": "110101199001011234",
    "licenseArchiveNumber": "A123456789",
    "tankCount": "双仓",
    "tank1LoadAmount": 15.00,
    "tank1OilType": "92#",
    "tank2LoadAmount": 15.00,
    "tank2OilType": "92#",
    "remark": "测试预约"
  }'

# 查询详情
curl -X GET "http://localhost:8080/exam/api/oil/reservation/detail/1234567890123456789"
```
