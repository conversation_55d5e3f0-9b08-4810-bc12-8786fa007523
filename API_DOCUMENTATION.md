# 油品提取预约接口文档

## 接口概述

本接口用于创建油品提取预约申请，支持以下功能：
- 新增油品提取预约
- 查询预约详情
- 数据验证和审批状态管理

## 接口地址

- **基础路径**: `/exam/api/oil/reservation`
- **新增预约**: `POST /exam/api/oil/reservation/save`
- **查询详情**: `GET /exam/api/oil/reservation/detail/{id}`
- **测试接口**: `GET /exam/api/oil/reservation/test`

## 新增预约接口

### 请求方式
```
POST /exam/api/oil/reservation/save
```

### 请求参数

```json
{
  "scheduledTime": "2024-12-20 14:30:00",
  "oilDepot": "北京石油库",
  "oilType": "92#",
  "oilTonnage": 30.00,
  "licensePlate": "京A12345",
  "driverName": "张三",
  "driverIdCard": "110101199001011234",
  "driverPhone": "13800138000",
  "driverLicenseNumber": "110101199001011234",
  "licenseArchiveNumber": "A123456789",
  "tankCount": "双仓",
  "tankLoadInfo": "1:15.00:92#;2:15.00:92#",
  "remark": "测试预约"
}
```

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scheduledTime | DateTime | 是 | 预定提油时间，格式：yyyy-MM-dd HH:mm:ss |
| oilDepot | String | 是 | 油库名称，最大100字符 |
| oilType | String | 是 | 油品种类，只能是：0#、92#、95# |
| oilTonnage | Decimal | 是 | 提油吨数，范围：0.01-999.99 |
| licensePlate | String | 是 | 车牌号，需符合中国车牌格式 |
| driverName | String | 是 | 司机姓名，最大50字符 |
| driverIdCard | String | 是 | 司机身份证号，18位身份证格式 |
| driverPhone | String | 是 | 司机手机号，11位手机号格式 |
| driverLicenseNumber | String | 是 | 司机驾驶证号，最大50字符 |
| licenseArchiveNumber | String | 是 | 驾驶证档案号，最大50字符 |
| tankCount | String | 是 | 车辆仓数，只能是：单仓、双仓、三仓 |
| tankLoadInfo | String | 是 | 各仓装载信息，格式见下方说明 |
| remark | String | 否 | 备注，最大500字符 |

### tankLoadInfo 格式说明

各仓装载信息使用字符串格式存储，格式为：
```
仓号1:装载量1:油品类型1;仓号2:装载量2:油品类型2;仓号3:装载量3:油品类型3
```

**示例：**
- 单仓：`1:30.00:92#`
- 双仓：`1:15.00:92#;2:15.00:95#`
- 三仓：`1:10.00:0#;2:10.00:92#;3:10.00:95#`

**验证规则：**
1. 仓号必须在1-3之间
2. 装载量必须大于0
3. 油品类型只能是0#、92#、95#
4. 各仓装载量总和必须等于提油吨数
5. 仓数必须与tankCount字段匹配

### 响应结果

**成功响应：**
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "1234567890123456789"
  }
}
```

**失败响应：**
```json
{
  "success": false,
  "code": 500,
  "message": "新增油品提取预约失败: 各仓装载量总和必须等于提油吨数",
  "data": null
}
```

## 查询详情接口

### 请求方式
```
GET /exam/api/oil/reservation/detail/{id}
```

### 路径参数
- `id`: 预约ID

### 响应结果

```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "1234567890123456789",
    "scheduledTime": "2024-12-20 14:30:00",
    "oilDepot": "北京石油库",
    "oilType": "92#",
    "oilTonnage": 30.00,
    "licensePlate": "京A12345",
    "driverName": "张三",
    "driverIdCard": "110101199001011234",
    "driverPhone": "13800138000",
    "driverLicenseNumber": "110101199001011234",
    "licenseArchiveNumber": "A123456789",
    "tankCount": "双仓",
    "tankLoadInfo": "1:15.00:92#;2:15.00:92#",
    "submitTime": "2024-12-19 10:30:00",
    "approvalStatus": 0,
    "approvalStatusDesc": "待审批",
    "createTime": "2024-12-19 10:30:00",
    "createBy": "系统用户",
    "remark": "测试预约"
  }
}
```

## 审批状态说明

| 状态值 | 状态描述 | 说明 |
|--------|----------|------|
| 0 | 待审批 | 预约已提交，等待审批 |
| 1 | 审批通过 | 预约已通过审批 |
| 2 | 审批拒绝 | 预约被拒绝 |

## 数据存储

数据存储在MongoDB中，集合名称为：`oil_reservation`

## 错误码说明

| 错误信息 | 说明 |
|----------|------|
| 预定提油时间不能是过去时间 | 预约时间必须是未来时间 |
| 各仓装载量总和必须等于提油吨数 | 装载信息验证失败 |
| 各仓装载信息数量与车辆仓数不匹配 | 仓数验证失败 |
| 各仓装载信息格式错误 | tankLoadInfo格式不正确 |
| 油品类型只能是0#、92#、95# | 油品类型验证失败 |

## 使用示例

### cURL 示例

```bash
# 新增预约
curl -X POST "http://localhost:8080/exam/api/oil/reservation/save" \
  -H "Content-Type: application/json" \
  -d '{
    "scheduledTime": "2024-12-20 14:30:00",
    "oilDepot": "北京石油库",
    "oilType": "92#",
    "oilTonnage": 30.00,
    "licensePlate": "京A12345",
    "driverName": "张三",
    "driverIdCard": "110101199001011234",
    "driverPhone": "13800138000",
    "driverLicenseNumber": "110101199001011234",
    "licenseArchiveNumber": "A123456789",
    "tankCount": "双仓",
    "tankLoadInfo": "1:15.00:92#;2:15.00:92#",
    "remark": "测试预约"
  }'

# 查询详情
curl -X GET "http://localhost:8080/exam/api/oil/reservation/detail/1234567890123456789"
```
