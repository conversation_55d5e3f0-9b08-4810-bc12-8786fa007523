spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: ***************************************************************************************************************
            username: root
            password: mysql@database
            initial-size: 10
            max-active: 100
            min-idle: 10
            max-wait: 60000
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            #Oracle需要打开注释
            #validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                #login-username: admin
                #login-password: admin
            filter:
                stat:
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: false
                wall:
                    config:
                        multi-statement-allow: true
    data:
        mongodb:
            host: **************
            port: 27017
            username: jvs
            password: jvs
            database: jvs-data
            authentication-database: admin
oss:
    accessKeyId: LTAI5tRpbrYbz2zkYxxAX2pt
    accessKeySecret: ******************************
    roleArn: acs:ram::1709232887969008:role/aliyunosstokengeneratorrole
    bucket: eps-file
    region: oss-cn-hangzhou
    endpoint: https://oss-cn-hangzhou.aliyuncs.c
fdfs:
    so-timeout: 1500
    connect-timeout: 600
    thumb-image:
        width: 150
        height: 150
    tracker-list:
        - **************:22122
    webServerUrl: http://**************:9000/

##多数据源的配置
dynamic:
  datasource:
    slave1:
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      url: ************************************************************
      username: sa
      password: 123456
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: ************************************************
#      username: renren
#      password: 123456