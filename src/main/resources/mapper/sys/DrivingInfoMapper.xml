<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kan.modules.sys.dao.DrivingInfoMapper">

    <select id="getCard" resultType="com.kan.modules.sys.dto.CakeResp">
        SELECT (SELECT COUNT(*) FROM t_driving_info WHERE card_end &gt; NOW()) as okNum,
               (SELECT COUNT(*) FROM t_driving_info WHERE card_end &lt; NOW()) as noNum
    </select>
    <select id="getDriving" resultType="com.kan.modules.sys.dto.CakeResp">
        SELECT (SELECT COUNT(*) FROM t_driving_info WHERE driving_end &gt; NOW()) as okNum,
               (SELECT COUNT(*) FROM t_driving_info WHERE driving_end &lt; NOW()) as noNum
    </select>
    <select id="getPractice" resultType="com.kan.modules.sys.dto.CakeResp">
        SELECT (SELECT COUNT(*) FROM t_driving_info WHERE practice_end &gt; NOW()) as okNum,
               (SELECT COUNT(*) FROM t_driving_info WHERE practice_end &lt; NOW()) as noNum
    </select>
    <select id="getOpenId" resultType="com.kan.modules.sys.dto.OpenTemp">
        SELECT * FROM `t_open_temp`
    </select>
</mapper>