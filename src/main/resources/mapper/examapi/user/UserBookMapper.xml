<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kan.modules.user.book.mapper.UserBookMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.kan.modules.user.book.entity.UserBook">
        <id column="id" property="id" />
        <result column="exam_id" property="examId" />
        <result column="user_id" property="userId" />
        <result column="qu_id" property="quId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="wrong_count" property="wrongCount" />
        <result column="title" property="title" />
        <result column="sort" property="sort" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`,`exam_id`,`user_id`,`qu_id`,`create_time`,`update_time`,`wrong_count`,`title`,`sort`
    </sql>

</mapper>
