<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kan.modules.datav.mapper.SubscribeMapper">
    <select id="getListByDate" resultType="com.kan.modules.datav.dto.SubscribeDto">
        SELECT *
        FROM (SELECT FORMAT(
                             SUM(a.income) / (SELECT MAX(total_count) AS total_count
                                              FROM (SELECT SUM(total_count) AS total_count,
                                                           SUM(on_line)     AS on_line,
                                                           create_time
                                                    FROM t_car_line
                                                    WHERE create_time LIKE CONCAT(a.create_time, '%')
                                                      AND dept_code=a.company_name
                                                    GROUP BY create_time) t), 2) AS ruZhi,
                     FORMAT(
                             SUM(a.income) / (SELECT MAX(on_line) AS on_line
                                              FROM (SELECT SUM(total_count) AS total_count,
                                                           SUM(on_line)     AS on_line,
                                                           create_time
                                                    FROM t_car_line
                                                    WHERE create_time LIKE CONCAT(a.create_time, '%')
                                                      AND dept_code=a.company_name
                                                    GROUP BY create_time) t), 2) AS liZhi,
                     DATE_FORMAT(a.create_time, "%m月%d日")                      AS faCheRiQi
              FROM `t_subscribe_copy1` AS a
              WHERE a.company_name=#{dept}
              GROUP BY a.create_time
              ORDER BY a.create_time DESC
              LIMIT 11) AS a
        ORDER BY faCheRiQi
    </select>
    <select id="getListAll" resultType="com.kan.modules.datav.dto.SubscribeDto">
        SELECT *
        FROM (SELECT FORMAT(
                             SUM(a.income) / (SELECT MAX(total_count) AS total_count
                                              FROM (SELECT SUM(total_count) AS total_count,
                                                           SUM(on_line)     AS on_line,
                                                           create_time
                                                    FROM t_car_line
                                                    WHERE create_time LIKE CONCAT(a.create_time, '%')
                                                    GROUP BY create_time) t), 2) AS ruZhi,
                     FORMAT(
                             SUM(a.income) / (SELECT MAX(on_line) AS on_line
                                              FROM (SELECT SUM(total_count) AS total_count,
                                                           SUM(on_line)     AS on_line,
                                                           create_time
                                                    FROM t_car_line
                                                    WHERE create_time LIKE CONCAT(a.create_time, '%')
                                                    GROUP BY create_time) t), 2) AS liZhi,
                     DATE_FORMAT(a.create_time, "%m月%d日")                      AS faCheRiQi
              FROM `t_subscribe_copy1` AS a
              GROUP BY a.create_time
              ORDER BY a.create_time DESC
              LIMIT 11) AS a
        ORDER BY faCheRiQi
    </select>
</mapper>