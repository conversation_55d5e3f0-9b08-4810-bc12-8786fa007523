<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kan.modules.datav.mapper.DriverInfoDayNumMapper">

    <select id="selectByDeptId" resultType="com.kan.modules.datav.dto.DriverInfoDayNumResp">
        SELECT * FROM (SELECT
        SUM( driver_num ) as num,
        day_time as dayTime
        FROM
        t_driver_day_num AS a
        <where>
        <if test="deptCode !=null and deptCode !=''">
         dept_code=#{deptCode}
        </if>
        </where>
        GROUP BY
        day_time
        order by
        limit 7 day_time desc
        ) t ORDER BY dayTime
    </select>
</mapper>