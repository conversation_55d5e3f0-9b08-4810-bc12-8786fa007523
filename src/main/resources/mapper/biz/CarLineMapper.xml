<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kan.modules.datav.CarLineMapper">

    <select id="selectTotalCount" resultType="com.kan.modules.datav.dto.CarLineEntity">
        SELECT totalCount,
               onLine,
               createTime
        FROM (SELECT sum(total_count)               as totalCount,
                     sum(on_line)                   as onLine,
                     DATE_FORMAT(create_time, "%H") as createTime,
                     create_time
              FROM `t_car_line`
              GROUP BY create_time
              ORDER BY create_time desc
              limit 24) as a
        ORDER BY create_time asc
    </select>
    <select id="selectByDept" resultType="com.kan.modules.datav.dto.CarLineEntity">
        SELECT totalCount,
               onLine,
               createTime
        FROM (select total_count                    as totalCount,
                     on_line                        as onLine,
                     DATE_FORMAT(create_time, "%H") as createTime,
                     create_time
              from t_car_line
              WHERE dept_code = #{deptCode}
              ORDER BY create_time desc
              limit 24) as a
        ORDER BY create_time asc
    </select>
</mapper>