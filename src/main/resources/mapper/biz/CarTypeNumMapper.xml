<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kan.modules.datav.utils.CarTypeNumMapper">

    <select id="getCarTypeNum" resultType="com.kan.modules.datav.dto.CarTypeNumDto">
        SELECT
        CASE type
        WHEN 0 THEN
        '油车'
        ELSE
        '电车'
        END as name,
        sum(car_num) as value
        FROM
        `t_car_type_num`
        <where>
            <if test="deptCode != null and deptCode !=''">
                dept_code = #{deptCode}
            </if>
        </where>
        GROUP BY type
    </select>
</mapper>