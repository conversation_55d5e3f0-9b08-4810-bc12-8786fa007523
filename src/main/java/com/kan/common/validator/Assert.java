

package com.kan.common.validator;

import com.kan.common.exception.KanException;
import org.apache.commons.lang.StringUtils;

/**
 * 数据校验
 *
 * <AUTHOR>
 */
public abstract class Assert {

    public static void isBlank(String str, String message) {
        if (StringUtils.isBlank(str)) {
            throw new KanException(message);
        }
    }

    public static void isNull(Object object, String message) {
        if (object == null) {
            throw new KanException(message);
        }
    }
}
