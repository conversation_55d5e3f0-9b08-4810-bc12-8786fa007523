package com.kan.common.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.alibaba.fastjson.JSONObject;
import com.kan.common.exception.KanException;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-01 15:20
 * @Version: 1.0
 */
public class JvsUtils {

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();
    public static String sendPostValidateUser(String userName,String password){

        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        okhttp3.RequestBody aNull = okhttp3.RequestBody.create(mediaType, "null");
        Request request = new Request.Builder()
                .url("https://frame.bctools.cn/auth/oauth2/token?" +
                        "username=" + userName+
                        "&password=" + password+
                        "&grant_type=password" +
                        "&scope=server" +
                        "&client_id=frame" +
                        "&client_secret=frame")
                .method("POST", aNull)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36")
                .addHeader("Accept", "application/json")
                .build();
        Response response = null;
        String string = null;
        try {
            response = HTTP_CLIENT.newCall(request).execute();
            string = response.body().string();
        } catch (IOException e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = JSONObject.parseObject(string);
        String userDto = jsonObject.get("userDto").toString();
        String s = decodedPassword(userDto);
        System.out.println(s);
        return s;
//    public static void sendPost(String userName,String password) throws Exception{
//        OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();
//        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
//        okhttp3.RequestBody aNull = okhttp3.RequestBody.create(mediaType, "null");
//        Request request = new Request.Builder()
//                .url("https://frame.bctools.cn/auth/oauth2/token?" +
//                        "username=wangchen" +
//                        "&password=13014d713abf32c5deb61d11252b6558" +
//                        "&grant_type=password" +
//                        "&scope=server" +
//                        "&client_id=frame" +
//                        "&client_secret=frame")
//                .method("POST", aNull)
//                .addHeader("Content-Type", "application/x-www-form-urlencoded")
//                .addHeader("User-Agent","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36")
//                .addHeader("Accept", "application/json")
//                .build();
//        Response response = HTTP_CLIENT.newCall(request).execute();
//        String string = response.body().string();
//        System.out.println(string);
    }

    public static void main(String[] args) {
        System.out.println(decodedPassword(
                "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"));

    }
    /**
     * 解密
     *
     * @param encodedPassword 密文
     * @return 明文
     */
    public static String decodedPassword(String encodedPassword) {
        try {
            AES aes = getKey();
            return aes.decryptStr(encodedPassword, StandardCharsets.UTF_8).trim();
        } catch (Exception e) {
            throw new KanException("解密失败");
        }
    }
    /**
     * 替换Key通过APPID获取前端加密key
     *
     * @return
     */
    private static AES getKey() {

        String key = Base64.encode("jvs");
        //超过16位，截取前面 16位
        int i = 16;
        if (key.length() >= i) {
            key = key.substring(0, i);
        }
        String format = String.format("%016d", 0);
        key += format.substring(key.length());
        byte[] bytes = key.getBytes(StandardCharsets.UTF_8);
        AES aes = new AES(Mode.CBC, Padding.ZeroPadding, bytes, bytes);
        return aes;
    }
}
