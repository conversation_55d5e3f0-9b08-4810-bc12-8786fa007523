package com.kan.common.utils;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.kan.common.exception.KanException;
import com.kan.modules.sys.dao.DrivingInfoMapper;
import com.kan.modules.sys.dao.DrivingInfoNotesMapper;
import com.kan.modules.sys.dto.DrivingInfoData;
import com.kan.modules.sys.dto.DrivingInfoEntity;
import com.kan.modules.sys.entity.TDrivingInfoNotes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

// 有个很重要的点 DemoDataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
@Slf4j
public class EasyUtils implements ReadListener<DrivingInfoData> {
    /**
     * 假设这个是一个DAO，当然有业务逻辑这个也可以是一个service。当然如果不用存储这个对象没用。
     */
    private DrivingInfoMapper drivingInfoMapper;
    private DrivingInfoNotesMapper drivingInfoNotesMapper;
    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 2000;

    /**
     * 缓存的数据
     */
    private List<DrivingInfoData> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);


    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     */
    public EasyUtils(DrivingInfoMapper drivingInfoMapper, DrivingInfoNotesMapper drivingInfoNotesMapper) {
        this.drivingInfoMapper = drivingInfoMapper;
        this.drivingInfoNotesMapper = drivingInfoNotesMapper;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(DrivingInfoData data, AnalysisContext context) {
//        System.out.println("数据"+JSON.toJSONString(data));
        cachedDataList.add(data);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    public List<DrivingInfoData> getDates() {
        return cachedDataList;
    }

    public void setDates(List<DrivingInfoData> datas) {
        this.cachedDataList = datas;
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());

        int count=0;
        StringBuilder msg= new StringBuilder();
        for (DrivingInfoData itemUploadDto : cachedDataList) {
            if ("示例".equals(itemUploadDto.getName())){
                continue;
            }
            DrivingInfoEntity drivingInfo  = drivingInfoMapper.selectOne(new QueryWrapper<DrivingInfoEntity>()
                    .eq("card_num", itemUploadDto.getCardNum()));
            if (!ObjectUtils.isEmpty(drivingInfo)){
                count=count+1;
                if (count==1) {
                    msg.append(drivingInfo.getName());
                }else {
                    msg.append("、").append(drivingInfo.getName());
                }
                continue;
            }
            DrivingInfoEntity drivingInfoEntity=new DrivingInfoEntity();
            BeanUtils.copyProperties(itemUploadDto,drivingInfoEntity);
            drivingInfoMapper.insert(drivingInfoEntity);
            for (int i = 1; i < 4; i++) {
                TDrivingInfoNotes drivingInfoNotes=new TDrivingInfoNotes();
                drivingInfoNotes.setKeyNum(String.valueOf(i));
                drivingInfoNotes.setDrivingInfoId(drivingInfoEntity.getId());
                drivingInfoNotesMapper.insert(drivingInfoNotes);
            }

        }
        log.info("存储数据库成功！");
        if (count > 0) {
            throw new KanException((cachedDataList.size()-count)+"条数据存储成功,驾驶员"+msg.append("已存在！"),200);
        }
    }

}