/*
 * Copyright (c) 2005, 2017, All rights reserved.
 * 
 *
 */
package com.kan.common.utils;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.rmi.ConnectException;
import java.util.*;
import java.util.Map.Entry;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;

import com.alibaba.fastjson.JSON;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

/**
 * 描述 Http请求工具类
 * 
 * @created 2017年3月1日 下午4:22:50
 */
public class PlatHttpUtil {

    /**
     * 发送https get请求
     * @param url
     * @param httpClient
     * @param charset
     * @return
     */
    public static String sendGetParams(String url,SSLClient httpClient,String charset,String type){
        HttpGet httpGet = null;
        String result = null;
        try{
            httpClient = new SSLClient();
            httpGet = new HttpGet(url);
           /* //设置参数
            StringEntity entity = new StringEntity(postBody,"utf-8");//解决中文乱码问题
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpGet.setEntity(entity);*/
            if (ObjectUtils.isNotEmpty(type)) {
                httpGet.setHeader("userid","test1Test");
                httpGet.setHeader("userkey","Ckzk123");
            }
            HttpResponse response = httpClient.execute(httpGet);
            if(response != null){
                HttpEntity resEntity = response.getEntity();
                if(resEntity != null){
                    result = EntityUtils.toString(resEntity,charset);
                }
            }
            httpClient.close();
            return result;
        }catch(Exception ex){
            ex.printStackTrace();
        }
        return result;
    }

    /**
     * 发送https请求
     * @param url
     * @return
     */
    public static String sendPostParams(String url,String postBody,
            SSLClient httpClient,String charset,String token){
        HttpPost httpPost = null;  
        String result = null;  
        try{  
            httpClient = new SSLClient();  
            httpPost = new HttpPost(url);  
            //设置参数  
            StringEntity entity = new StringEntity(postBody,"utf-8");//解决中文乱码问题    
            entity.setContentEncoding("UTF-8");    
            entity.setContentType("application/json");
            if (!ObjectUtils.isEmpty(token)) {
                httpPost.setHeader("accessToken", token);
            }
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);  
            if(response != null){  
                HttpEntity resEntity = response.getEntity();  
                if(resEntity != null){  
                    result = EntityUtils.toString(resEntity,charset);  
                }  
            }  
            httpClient.close();
            return result;
        }catch(Exception ex){  
            ex.printStackTrace();  
        }  
        return result;
    }
    public static String sendPostHttp(String url,String postBody,String type){
        String resp=null;
        try {
            HttpClient httpClient = new HttpClient();
            PostMethod postMethod = new PostMethod(url);
            postMethod.setRequestBody(postBody);
            if (ObjectUtils.isNotEmpty(type)) {
                postMethod.addRequestHeader("userid","test1Test");
                postMethod.addRequestHeader("userkey","Ckzk123");
            }
            postMethod.addRequestHeader("Content-Type","application/json");
            httpClient.executeMethod(postMethod);
            resp= postMethod.getResponseBodyAsString();
        }catch (Exception ex){
            ex.printStackTrace();
        }
        return resp;
    }
    /**
     * 发送https请求
     * @param url
     * @return
     */
    public static String sendPutParams(String url,String postBody,
                                        SSLClient httpClient,String charset,String token){
        HttpPut httpPost = null;
        String result = null;
        try{
            httpClient = new SSLClient();
            httpPost = new HttpPut(url);
            //设置参数
            StringEntity entity = new StringEntity(postBody,"utf-8");//解决中文乱码问题
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            if (!ObjectUtils.isEmpty(token)) {
                httpPost.setHeader("accessToken", token);
            }
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            if(response != null){
                HttpEntity resEntity = response.getEntity();
                if(resEntity != null){
                    result = EntityUtils.toString(resEntity,charset);
                }
            }
            httpClient.close();
            return result;
        }catch(Exception ex){
            ex.printStackTrace();
        }
        return result;
    }
    /**
     * 发送https请求
     * @param url
     * @param params
     * @return
     */
    public static String sendPostParams(String url,Map<String,Object> params,
            SSLClient httpClient,String charset){
        HttpPost httpPost = null;
        String result = null;
        try{
            httpClient = new SSLClient();
            httpPost = new HttpPost(url);
            //设置参数
            List<NameValuePair> list = new ArrayList<NameValuePair>();
            Iterator iterator = params.entrySet().iterator();
            while(iterator.hasNext()){
                Entry<String,String> elem = (Entry<String, String>) iterator.next();
                list.add(new BasicNameValuePair(elem.getKey(),elem.getValue()));
            }
            if(list.size() > 0){
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list,charset);
                httpPost.setEntity(entity);
            }
            HttpResponse response = httpClient.execute(httpPost);
            if(response != null){
                HttpEntity resEntity = response.getEntity();
                if(resEntity != null){
                    result = EntityUtils.toString(resEntity,charset);
                }
            }
            httpClient.close();
            return result;
        }catch(Exception ex){
            ex.printStackTrace();
        }
        return result;
    }

    /**
     * 发送http请求
     * @param url 地址
     * @param params  参数
     * @param httpclient client对象
     * @return
     */
    public static String sendPostParams(String url,Map<String,Object> params,
            DefaultHttpClient httpclient,HttpPost httpost){
        BasicResponseHandler responseHandler = new BasicResponseHandler();
        // 添加参数
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        if (params != null && params.keySet().size() > 0) {
            Iterator iterator = params.entrySet().iterator();
            while (iterator.hasNext()) {
                Entry<String,Object> entry = (Entry<String,Object>) iterator.next();
                nvps.add(new BasicNameValuePair((String) entry.getKey(),
                        (String) entry.getValue()));
            }
        }
        httpost.setEntity(new UrlEncodedFormEntity(nvps, Consts.UTF_8));
        try {
            return httpclient.execute(httpost,responseHandler).toString();
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * POST参数
     * @param url
     * @param params
     * @return
     */
    public static String sendPostParams(String url,Map<String,Object> params){
        DefaultHttpClient httpclient = new DefaultHttpClient(
                new ThreadSafeClientConnManager());
        HttpPost httpost = new HttpPost(url);
        BasicResponseHandler responseHandler = new BasicResponseHandler();
        // 添加参数
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        if (params != null && params.keySet().size() > 0) {
            Iterator iterator = params.entrySet().iterator();
            while (iterator.hasNext()) {
                Entry<String,Object> entry = (Entry<String,Object>) iterator.next();
                nvps.add(new BasicNameValuePair((String) entry.getKey(),
                        (String) entry.getValue()));
            }
        }
        httpost.setEntity(new UrlEncodedFormEntity(nvps, Consts.UTF_8));
        try {
            String result =  httpclient.execute(httpost,responseHandler).toString();
            httpclient.getConnectionManager().shutdown();
            return result;
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

//    /**
//     * 发送http请求
//     * @param url 地址
//     * @param params  参数
//     * @param httpclient client对象
//     * @return
//     */
//    public static String sendPostParams(String url,Map<String,Object> params,DefaultHttpClient httpclient){
//        HttpPost httpost = new HttpPost(url);
//        BasicResponseHandler responseHandler = new BasicResponseHandler();
//        // 添加参数
//        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
//        if (params != null && params.keySet().size() > 0) {
//            Iterator iterator = params.entrySet().iterator();
//            while (iterator.hasNext()) {
//                Entry<String,Object> entry = (Entry<String,Object>) iterator.next();
//                nvps.add(new BasicNameValuePair((String) entry.getKey(),
//                        (String) entry.getValue()));
//            }
//        }
//        httpost.setEntity(new UrlEncodedFormEntity(nvps, Consts.UTF_8));
//        try {
//            return httpclient.execute(httpost,responseHandler).toString();
//        } catch (ClientProtocolException e) {
//            e.printStackTrace();
//            //PlatLogUtil.printStackTrace(e);
//        } catch (IOException e) {
//            e.printStackTrace();
//            //PlatLogUtil.printStackTrace(e);
//        }
//        return null;
//    }
    /**
     *
     * @param url
     * @param postBody
     * @param encode
     * @return
     */
    public static String postParams(String url,String postBody,String encode){
        if(StringUtils.isEmpty(encode)){
            encode = "UTF-8";
        }
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        try {
            StringEntity entity = new StringEntity(postBody,encode);//解决中文乱码问题
            entity.setContentEncoding(encode);
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            HttpResponse resp = client.execute(httpPost);
            String result = null;
            if(resp.getStatusLine().getStatusCode() == 200) {
                 HttpEntity he = resp.getEntity();
                 result = EntityUtils.toString(he,encode);
            }
            client.close();
            return result;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (ParseException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * POST参数到URL
     * @param url
     * @param postBody
     * @return
     */
    public static String postParams(String url,String postBody){
        return postParams(url,postBody,"UTF-8");
    }

    /***
     * httpGet请求工具类
     * @param urlStr
     * @param content  name=xxx&pwd=xxx
     * @param encoding
     * @return
     */
    public static String httpGetRequest(String urlStr, String content, String encoding) {
        URL url = null;
        HttpURLConnection connection = null;
        try {
            url = new URL(urlStr);
            connection = (HttpURLConnection) url.openConnection();// 新建连接实例
            connection.setConnectTimeout(2000);// 设置连接超时时间，单位毫秒
            connection.setReadTimeout(2000);// 设置读取数据超时时间，单位毫秒
            connection.setDoOutput(true);// 是否打开输出流 true|false
            connection.setDoInput(true);// 是否打开输入流true|false
            connection.setRequestMethod("GET");// 提交方法POST|GET
            connection.setUseCaches(false);// 是否缓存true|false
            connection.connect();// 打开连接端口
            DataOutputStream out = new DataOutputStream(
                    connection.getOutputStream());// 打开输出流往对端服务器写数据
            out.writeBytes(content);// 写数据,也就是提交你的表单 name=xxx&pwd=xxx
            out.flush();// 刷新
            out.close();// 关闭输出流
            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    connection.getInputStream(), encoding));// 往对端写完数据对端服务器返回数据
            // ,以BufferedReader流来读取
            StringBuffer buffer = new StringBuffer();
            String line = "";
            while ((line = reader.readLine()) != null) {
                buffer.append(line);
            }
            reader.close();
            return buffer.toString();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();// 关闭连接
            }
        }
        return null;
    }

    /**
     * 下载文件
     * @param fileUrl :访问地址
     * @param localFilePath :本地存储路径
     */
    public static void downloadFile(String fileUrl,String localFilePath){
        String folderPath = localFilePath.substring(0, localFilePath.lastIndexOf("/"));
        File fileFolder = new File(folderPath);
        if (!fileFolder.exists()) {
            fileFolder.mkdirs();
        }
        try {
            HttpClient client = new HttpClient();
            GetMethod get = new GetMethod(fileUrl);
            client.executeMethod(get);
            File storeFile = new File(localFilePath);
            FileOutputStream output = new FileOutputStream(storeFile);
            //得到网络资源的字节数组,并写入文件
            output.write(get.getResponseBody());
            output.close();
        } catch (HttpException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (FileNotFoundException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * 进行文件的上传
     * @param uploadUrl 文件的地址
     * @param filePath 文件的路径
     * @param postParams 需要额外POST的参数
     * @return
     */
    public static String uploadFile(String uploadUrl,String filePath,Map<String,Object> postParams){
     // TODO Auto-generated method stub
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        CloseableHttpResponse httpResponse = null;
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(200000).setSocketTimeout(200000000).build();
        HttpPost httpPost = new HttpPost(uploadUrl);
        httpPost.setConfig(requestConfig);
        MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
        File file = new File(filePath);
        multipartEntityBuilder.addBinaryBody("file",file);
        ContentType contentType = ContentType.create("text/plain",Charset.forName("UTF-8"));
        if (postParams != null && postParams.keySet().size() > 0) {
            Iterator iterator = postParams.entrySet().iterator();
            while (iterator.hasNext()) {
                Entry<String,Object> entry = (Entry<String,Object>) iterator.next();
                multipartEntityBuilder.addTextBody((String) entry.getKey(),
                        (String) entry.getValue(),contentType);
            }
        }
        HttpEntity httpEntity = multipartEntityBuilder.build();
        httpPost.setEntity(httpEntity);
        String result = null;
        try {
            httpResponse = httpClient.execute(httpPost);
            HttpEntity responseEntity = httpResponse.getEntity();
            int statusCode= httpResponse.getStatusLine().getStatusCode();
            if(statusCode == 200){
                BufferedReader reader = new BufferedReader(new InputStreamReader(responseEntity.getContent()));
                StringBuffer buffer = new StringBuffer();
                String str = "";
                if(StringUtils.isNotEmpty(str = reader.readLine())){
                    buffer.append(str);
                }
                result = buffer.toString();
            }
            httpClient.close();
            if(httpResponse!=null){
                httpResponse.close();
            }
        } catch (ClientProtocolException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (UnsupportedOperationException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return result;
    }

    
}
