

package com.kan.common.utils;

import org.apache.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * 返回数据
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
public class Result extends HashMap<String, Object> {
	private static final long serialVersionUID = 1L;
	
	public Result() {
		put("code", 0);
		put("msg", "success");
	}
	public static Result addError() {
		return error(HttpStatus.SC_INTERNAL_SERVER_ERROR, "保存失败");
	}
	public static Result deleteError() {
		return error(HttpStatus.SC_INTERNAL_SERVER_ERROR, "删除失败");
	}
	public static Result error() {
		return error(HttpStatus.SC_INTERNAL_SERVER_ERROR, "未知异常，请联系管理员");
	}
	
	public static Result error(String msg) {
		return error(HttpStatus.SC_INTERNAL_SERVER_ERROR, msg);
	}
	
	public static Result error(int code, String msg) {
		Result r = new Result();
		r.put("code", code);
		r.put("msg", msg);
		return r;
	}

	public static Result ok(String msg) {
		Result r = new Result();
		r.put("msg", msg);
		return r;
	}
	
	public static Result ok(Map<String, Object> map) {
		Result r = new Result();
		r.putAll(map);
		return r;
	}
	
	public static Result ok() {
		return new Result();
	}
	public Result data(Object data) {
		this.put("data", data);
		return this;
	}

	public Result put(String key, Object value) {
		super.put(key, value);
		return this;
	}

	public Result appOk(Object data) {
		this.put("code", 200);
		this.put("data", data);
		return this;
	}
}
