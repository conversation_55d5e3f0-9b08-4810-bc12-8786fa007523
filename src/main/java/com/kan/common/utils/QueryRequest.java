package com.kan.common.utils;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class QueryRequest implements Serializable {

    private static final long serialVersionUID = -4869594085374385813L;
    /**
     * 当前页面数据量
     */
    @TableField(exist = false)
    private int pageSize=10;
    /**
     * 当前页码
     */
    @TableField(exist = false)
    private int pageNum=1;
}