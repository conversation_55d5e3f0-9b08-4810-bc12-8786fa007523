package com.kan.modules.user;

import com.kan.core.api.ApiError;
import com.kan.core.exception.ServiceException;
import com.kan.modules.sys.entity.SysUserEntity;
import org.apache.shiro.SecurityUtils;

/**
 * 用户静态工具类
 * <AUTHOR>
 */
public class UserUtils {


    /**
     * 获取当前登录用户的ID
     * @param throwable
     * @return
     */
    public static String getUserId(boolean throwable){
        try {
            return String.valueOf(((SysUserEntity) SecurityUtils.getSubject().getPrincipal()).getUserId());
        }catch (Exception e){
            if(throwable){
                throw new ServiceException(ApiError.ERROR_10010002);
            }
            return null;
        }
    }

    /**
     * 获取当前登录用户的ID
     * @param throwable
     * @return
     */
    public static boolean isAdmin(boolean throwable){
        try {
            SysUserEntity dto = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal());
            return dto.getUsername().contains("admin");
        }catch (Exception e){
            if(throwable){
                throw new ServiceException(ApiError.ERROR_10010002);
            }
        }

        return false;
    }

    /**
     * 获取当前登录用户的ID，默认是会抛异常的
     * @return
     */
    public static String getUserId(){
        return getUserId(true);
    }
}
