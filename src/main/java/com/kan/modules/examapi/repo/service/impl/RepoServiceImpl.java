package com.kan.modules.examapi.repo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kan.core.api.dto.PagingReqDTO;
import com.kan.core.utils.BeanMapper;
import com.kan.modules.examapi.repo.dto.RepoDTO;
import com.kan.modules.examapi.repo.dto.request.RepoReqDTO;
import com.kan.modules.examapi.repo.dto.response.RepoRespDTO;
import com.kan.modules.examapi.repo.entity.Repo;
import com.kan.modules.examapi.repo.mapper.RepoMapper;
import com.kan.modules.examapi.repo.service.RepoService;
import org.springframework.stereotype.Service;

/**
* <p>
* 语言设置 服务实现类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 13:23
*/
@Service
public class RepoServiceImpl extends ServiceImpl<RepoMapper, Repo> implements RepoService {

    @Override
    public IPage<RepoRespDTO> paging(PagingReqDTO<RepoReqDTO> reqDTO) {
        return baseMapper.paging(reqDTO.toPage(), reqDTO.getParams());
     }

    @Override
    public void save(RepoDTO reqDTO) {

        //复制参数
        Repo entity = new Repo();
        BeanMapper.copy(reqDTO, entity);
        this.saveOrUpdate(entity);
    }
}
