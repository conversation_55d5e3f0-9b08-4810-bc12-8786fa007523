package com.kan.modules.examapi.repo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.kan.core.api.dto.PagingReqDTO;
import com.kan.modules.examapi.repo.dto.RepoDTO;
import com.kan.modules.examapi.repo.dto.request.RepoReqDTO;
import com.kan.modules.examapi.repo.dto.response.RepoRespDTO;
import com.kan.modules.examapi.repo.entity.Repo;

/**
* <p>
* 题库业务类
* </p>
*
* <AUTHOR>
* @since 2020-05-25 13:23
*/
public interface RepoService extends IService<Repo> {

    /**
    * 分页查询数据
    * @param reqDTO
    * @return
    */
    IPage<RepoRespDTO> paging(PagingReqDTO<RepoReqDTO> reqDTO);


    /**
     * 保存
     * @param reqDTO
     */
    void save(RepoDTO reqDTO);
}
