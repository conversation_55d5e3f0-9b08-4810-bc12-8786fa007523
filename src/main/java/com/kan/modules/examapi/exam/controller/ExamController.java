package com.kan.modules.examapi.exam.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kan.common.utils.Result;
import com.kan.core.api.controller.BaseController;
import com.kan.core.api.dto.BaseIdReqDTO;
import com.kan.core.api.dto.BaseIdsReqDTO;
import com.kan.core.api.dto.BaseStateReqDTO;
import com.kan.core.api.dto.PagingReqDTO;
import com.kan.modules.examapi.exam.dto.ExamDTO;
import com.kan.modules.examapi.exam.dto.request.ExamSaveReqDTO;
import com.kan.modules.examapi.exam.dto.response.ExamOnlineRespDTO;
import com.kan.modules.examapi.exam.dto.response.ExamReviewRespDTO;
import com.kan.modules.examapi.exam.entity.Exam;
import com.kan.modules.examapi.exam.service.ExamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
* <p>
* 考试控制器
* </p>
*
* <AUTHOR>
* @since 2020-07-25 16:18
*/
@Api(tags={"考试"})
@RestController
@RequestMapping("/exam/api/exam/exam")
public class ExamController extends BaseController {

    @Autowired
    private ExamService examService;

    /**
    * 添加或修改
    * @param reqDTO
    * @return
    */
    @ApiOperation(value = "添加或修改")
    @RequestMapping(value = "/save", method = { RequestMethod.POST})
    public Result save(@RequestBody ExamSaveReqDTO reqDTO) {
        //复制参数
        examService.save(reqDTO);
        return new Result();
    }

    /**
    * 批量删除
    * @param reqDTO
    * @return
    */
    @ApiOperation(value = "批量删除")
    @RequestMapping(value = "/delete", method = { RequestMethod.POST})
    public Result edit(@RequestBody BaseIdsReqDTO reqDTO) {
        //根据ID删除
        examService.removeByIds(reqDTO.getIds());
        return new Result();
    }

    /**
    * 查找详情
    * @param reqDTO
    * @return
    */
    @ApiOperation(value = "查找详情")
    @RequestMapping(value = "/detail", method = { RequestMethod.POST})
    public Result find(@RequestBody BaseIdReqDTO reqDTO) {
        ExamSaveReqDTO dto = examService.findDetail(reqDTO.getId());
        return new Result().data(dto);
    }

    /**
     * 查找详情
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "查找详情")
    @RequestMapping(value = "/state", method = { RequestMethod.POST})
    public Result state(@RequestBody BaseStateReqDTO reqDTO) {

        QueryWrapper<Exam> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(Exam::getId, reqDTO.getIds());
        Exam exam = new Exam();
        exam.setState(reqDTO.getState());
        exam.setUpdateTime(new Date());

        examService.update(exam, wrapper);
        return new Result();
    }


    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "考试视角")
    @RequestMapping(value = "/online-paging", method = { RequestMethod.POST})
    public Result myPaging(@RequestBody PagingReqDTO<ExamDTO> reqDTO) {

        //分页查询并转换
        IPage<ExamOnlineRespDTO> page = examService.onlinePaging(reqDTO);
        return new Result().data(page);
    }

    /**
    * 分页查找
    * @param reqDTO
    * @return
    */
    @ApiOperation(value = "分页查找")
    @RequestMapping(value = "/paging", method = { RequestMethod.POST})
    public Result paging(@RequestBody PagingReqDTO<ExamDTO> reqDTO) {

        //分页查询并转换
        IPage<ExamDTO> page = examService.paging(reqDTO);

        return new Result().data(page);
    }


    /**
     * 分页查找
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "待阅试卷")
    @RequestMapping(value = "/review-paging", method = { RequestMethod.POST})
    public Result reviewPaging(@RequestBody PagingReqDTO<ExamDTO> reqDTO) {

        //分页查询并转换
        IPage<ExamReviewRespDTO> page = examService.reviewPaging(reqDTO);

        return new Result().data(page);
    }


}
