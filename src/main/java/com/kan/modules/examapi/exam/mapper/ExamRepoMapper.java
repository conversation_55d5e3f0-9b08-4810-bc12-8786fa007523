package com.kan.modules.examapi.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kan.modules.examapi.exam.dto.ext.ExamRepoExtDTO;
import com.kan.modules.examapi.exam.entity.ExamRepo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <p>
* 考试题库Mapper
* </p>
*
* <AUTHOR>
* @since 2020-09-05 11:14
*/
@Mapper
public interface ExamRepoMapper extends BaseMapper<ExamRepo> {

    /**
     * 查找考试题库列表
     * @param examId
     * @return
     */
    List<ExamRepoExtDTO> listByExam(@Param("examId") String examId);
}
