package com.kan.modules.app.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class WechatUtil {

    public static JSONObject getSessionKeyOrOpenId(String code) {
        String requestUrl = "https://api.weixin.qq.com/sns/jscode2session";
        Map<String, String> requestUrlParam = new HashMap<>();
        // https://mp.weixin.qq.com/wxopen/devprofile?action=get_profile&token=164113089&lang=zh_CN
        //小程序appId
        requestUrlParam.put("appid", "wx4a3a60c42b1fa616");
        //小程序secret
        requestUrlParam.put("secret","fe19d94776d898b256541a8a204f0541");
        //小程序端返回的code
        requestUrlParam.put("js_code", code);
        //默认参数
        requestUrlParam.put("grant_type", "authorization_code");
        //发送post请求读取调用微信接口获取openid用户唯一标识
        JSONObject jsonObject = JSON.parseObject(HttpClientUtil.doPost(requestUrl, requestUrlParam));
        return jsonObject;
    }
    public static String insertUserRoom(String code) {
        String WX_URL = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code";
        try {
            if (StringUtils.isBlank(code)) {
                System.out.println("code为空");
                return "code为空";
            } else {
                String requestUrl = WX_URL.replace("APPID", "wx4a3a60c42b1fa616")
                        .replace("SECRET", "fe19d94776d898b256541a8a204f0541")
                        .replace("CODE", code).replace("authorization_code", "authorization_code");
                JSONObject jsonObject = JSON.parseObject(HttpClientUtil.doGet(requestUrl));
                if (jsonObject != null) {
                    try {
                        // 业务操作
                        String access_token = jsonObject.getString("access_token");
                        String openId = jsonObject.getString("openid");
                        //拉取用户信息
                        return openId;
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    System.out.println("code无效");
                    return "code无效";
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "error";
    }
}
