package com.kan.modules.app.utils;

import com.kan.modules.oss.service.FdfsClientService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

@Component
public class UEditorUpload {
    private Logger log = LoggerFactory.getLogger(UEditorUpload.class);
    private String path = ClassUtils.getDefaultClassLoader().getResource("").getPath();
    @Resource
    FdfsClientService fdfsClientService;

    public UEditorFile uploadImage(MultipartFile file) throws IOException {
        log.info("UEditor开始上传文件");
        Map<String, Object> map = fdfsClientService.uploadFileMap(file);
        String url = (String) map.get("fileUrl");
        //获取文件名
        String fileName = file.getOriginalFilename();
        UEditorFile uEditorFile = new UEditorFile();
        uEditorFile.setState("SUCCESS");
        //访问URL
        uEditorFile.setUrl(url);
        uEditorFile.setTitle(fileName);
        uEditorFile.setOriginal(fileName);
        return uEditorFile;
    }
    public UEditorFile uploadOss(MultipartFile file) throws IOException {
        log.info("UEditor开始上传文件");
        Map<String, Object> map = fdfsClientService.ossUploadFile(file);
        String url = (String) map.get("fileUrl");
        //获取文件名
        String fileName = file.getOriginalFilename();
        UEditorFile uEditorFile = new UEditorFile();
        uEditorFile.setState("SUCCESS");
        //访问URL
        uEditorFile.setUrl(url);
        uEditorFile.setTitle(fileName);
        uEditorFile.setOriginal(fileName);
        return uEditorFile;
    }

}
