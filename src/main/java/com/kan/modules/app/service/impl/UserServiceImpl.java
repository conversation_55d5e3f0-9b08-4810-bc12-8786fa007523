

package com.kan.modules.app.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kan.common.exception.KanException;
import com.kan.common.validator.Assert;
import com.kan.modules.app.dao.UserDao;
import com.kan.modules.app.entity.UserEntity;
import com.kan.modules.app.form.LoginForm;
import com.kan.modules.app.form.WeChatModel;
import com.kan.modules.app.service.UserService;
import com.kan.modules.app.utils.JwtUtils;
import com.kan.modules.app.utils.WechatUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Service("userService")
public class UserServiceImpl extends ServiceImpl<UserDao, UserEntity> implements UserService {
	@Autowired
	private JwtUtils jwtUtils;
	@Override
	public UserEntity queryByMobile(String mobile) {
		return baseMapper.selectOne(new QueryWrapper<UserEntity>().eq("mobile", mobile));
	}

	@Override
	public long login(LoginForm form) {
		UserEntity user = queryByMobile(form.getMobile());
		Assert.isNull(user, "手机号或密码错误");

		//密码错误
		if(!user.getPassword().equals(DigestUtils.sha256Hex(form.getPassword()))){
			throw new KanException("手机号或密码错误");
		}

		return user.getUserId();
	}
	@Override
	public Map<String, Object> userLogin(WeChatModel weChatModel) {
		// 1.接收小程序发送的code
		// 2.开发者服务器 登录凭证校验接口 appi + appsecret + code
//		JSONObject SessionKeyOpenId = WechatUtil.getSessionKeyOrOpenId(weChatModel.getCode());
		String openid = WechatUtil.insertUserRoom(weChatModel.getCode());
		// 3.接收微信接口服务 获取返回的参数
//		String openid = SessionKeyOpenId.getString("openid");
//		String sessionKey = SessionKeyOpenId.getString("session_key");
//
//		// 4.校验签名 小程序发送的签名signature与服务器端生成的签名signature2 = sha1(rawData + sessionKey)
//		String signature2 = DigestUtils.sha1Hex(weChatModel.getRawData() + sessionKey);
//		if (!weChatModel.getSignature().equals(signature2)) {
//			return Result.error().data("签名校验失败");
//		}
		// 5.根据返回的User实体类，判断用户是否是新用户，是的话，将用户信息存到数据库；
		LambdaQueryWrapper<UserEntity> lqw = Wrappers.lambdaQuery();
		if (ObjectUtils.isEmpty(openid)){
			throw new KanException("用户信息获取失败,请重新进入小程序!");
		}
		lqw.eq(UserEntity::getOpenId, openid);
		UserEntity user = baseMapper.selectOne(lqw);
		Map<String, Object> map = new HashMap<>();
		if (ObjectUtils.isEmpty(user)) {
			// 用户信息入库
			user = new UserEntity();
			user.setOpenId(openid);
			user.setCreateTime(new Date());

			baseMapper.insert(user);
		}
		map.put("user", user);
		if (ObjectUtils.isEmpty(user.getUsername())){
			user.setUsername("微信用户");
		}
		//生成token
		String token = jwtUtils.generateToken(user.getUserId());
		map.put("token", token);
		return map;
	}
}
