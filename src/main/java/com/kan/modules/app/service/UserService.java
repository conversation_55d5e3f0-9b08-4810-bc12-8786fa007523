

package com.kan.modules.app.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.kan.modules.app.entity.UserEntity;
import com.kan.modules.app.form.LoginForm;
import com.kan.modules.app.form.WeChatModel;

import java.util.Map;

/**
 * 用户
 *
 * <AUTHOR>
 */
public interface UserService extends IService<UserEntity> {

	UserEntity queryByMobile(String mobile);

	/**
	 * 用户登录
	 * @param form    登录表单
	 * @return        返回用户ID
	 */
	long login(LoginForm form);

	/**
	 * 验证微信登录
	 * @param weChatModel
	 * @return
	 */
	Map<String, Object> userLogin(WeChatModel weChatModel);

}
