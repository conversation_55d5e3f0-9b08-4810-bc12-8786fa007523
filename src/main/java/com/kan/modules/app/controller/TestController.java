package com.kan.modules.app.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kan.common.utils.Result;
import io.swagger.annotations.Api;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/3/1 15:50
 * @Version: 1.0
 */
@RestController
@RequestMapping("/weather")
public class TestController {

    @PostMapping("/now")
    public Result weatherNow(){
        /* district_id 城市编码：
         500100重庆  500101万州  500102涪陵  500103渝中  500104大渡口  500105江北  500106沙坪坝  500107九龙坡
         500108南岸  500109北碚  500110綦江  500111大足  500112渝北  500113巴南  500114黔江
         500115长寿  500116江津  500117合川  500118永川  500119南川  500120璧山  500151铜梁  500152潼南
         500153荣昌  500154开州  500155梁平  500156武隆  500229城口  500230丰都  500231垫江  500233忠县
         500235云阳  500236奉节  500237巫山  500238巫溪  500240石柱  500241秀山  500242酉阳  500243彭水
         */
        Map paraMap = new HashMap();
        RestTemplate restTemplate = new RestTemplate();
        String s = restTemplate.getForObject("https://api.map.baidu.com/weather/v1/?district_id=500112&data_type=now" +
                "&ak=8GKw6OyRvwdLS9ZgClqf2osb18otuk56",String.class, paraMap);
        return new Result().data(s);
    }
}
