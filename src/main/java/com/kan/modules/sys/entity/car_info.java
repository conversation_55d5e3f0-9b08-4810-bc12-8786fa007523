package com.kan.modules.sys.entity;


import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;

@Data
public class car_info implements Serializable {
    private static final long serialVersionUID = 7523059264202861246L;

    private String id;

    private String touBaoRiQi;
    
    private String houGongHao;
    
    private String cheLiangJingYingMoShi;

}

