package com.kan.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kan.modules.sys.dao.AcdMapper;
import com.kan.modules.sys.dto.TAcd;
import com.kan.modules.sys.service.AcdService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/5/17 16:47
 * @Version: 1.0
 */
@Service
public class AcdServiceImpl implements AcdService {
    @Resource
    AcdMapper acdMapper;

    @Override
    public IPage<TAcd> selectPage(String pageNum, String pageSize) {
        int anInt = Integer.parseInt(pageNum);
        int parseInt = Integer.parseInt(pageSize);
        LambdaQueryWrapper<TAcd> queryWrapper=new LambdaQueryWrapper<>();
        anInt=(anInt - 1) * parseInt;
        IPage<TAcd> page = new Page();
        Integer integer = acdMapper.selectCount(queryWrapper);
        queryWrapper.last("limit " + anInt + "," + parseInt);
        List<TAcd> codeConfigs = acdMapper.selectList(queryWrapper);
        page.setRecords(codeConfigs);
        page.setTotal(integer);
        return page;
    }
}
