package com.kan.modules.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kan.common.utils.Result;
import com.kan.modules.sys.dto.CakeListResp;
import com.kan.modules.sys.dto.DrivingInfoEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-08-30 20:55
 * @Version: 1.0
 */
public interface DrivingInfoService {

    IPage<DrivingInfoEntity> getList(DrivingInfoEntity drivingInfoEntity);

    Boolean add(DrivingInfoEntity drivingInfoEntity);

    void export(HttpServletResponse response, DrivingInfoEntity drivingInfoEntity);

    Result getUpload(MultipartFile file) throws IOException;

    CakeListResp cake();

    Boolean delete(DrivingInfoEntity drivingInfoEntity);
}
