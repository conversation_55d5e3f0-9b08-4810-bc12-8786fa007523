package com.kan.modules.sys.service.impl;

import com.kan.common.exception.KanException;
import com.kan.common.utils.JwtUtils;
import com.kan.common.utils.Result;
import com.kan.core.api.ApiError;
import com.kan.modules.sys.dto.MongoUser;
import com.kan.modules.sys.dto.SysUserLoginDTO;
import com.kan.modules.sys.entity.SysUserTokenEntity;
import com.kan.modules.sys.oauth2.TokenGenerator;
import com.kan.modules.sys.service.CaptchaService;
import com.kan.modules.sys.service.MongoUserService;
import com.kan.modules.sys.service.SysUserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * MongoDB用户服务实现类
 */
@Slf4j
@Service
public class MongoUserServiceImpl implements MongoUserService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired(required = false)
    private CaptchaService captchaService;

    @Override
    public SysUserLoginDTO login(String yongHuZhangHu, String miMa, String captcha, String key) {
        
        // 验证参数
        if (StringUtils.isBlank(yongHuZhangHu)) {
            throw new KanException(ApiError.ERROR_10010001.msg);
        }
        if (StringUtils.isBlank(miMa)) {
            throw new KanException(ApiError.ERROR_10010001.msg);
        }

        // 验证验证码（如果提供了验证码）
        if (StringUtils.isNotBlank(captcha) && !verifyCaptcha(key,captcha)) {
            throw new KanException(ApiError.ERROR_10010012.msg);
        }

        // 查找用户
        MongoUser user = findByYongHuZhangHu(yongHuZhangHu);
        if (user == null) {
            throw new KanException(ApiError.ERROR_90010001.msg);
        }

        // 检查用户是否被删除
        if (user.getDelFlag() != null && user.getDelFlag()) {
            throw new KanException(ApiError.ERROR_90010005.msg);
        }

        // 验证密码
        if (!verifyPassword(miMa, user.getMiMa())) {
            throw new KanException(ApiError.ERROR_90010002.msg);
        }

        // 生成登录响应
        return generateLoginResponse(user);
    }

    @Override
    public MongoUser findByYongHuZhangHu(String yongHuZhangHu) {
        try {
            log.info("开始查询用户，yongHuZhangHu: {}", yongHuZhangHu);

            // 获取当前数据库信息
            String currentDb = mongoTemplate.getDb().getName();
            log.info("当前连接的数据库: {}", currentDb);

            // 先测试连接和集合是否存在
            String collectionName = "444d5fec7d57c38f8e270f028acfca9748d469";
            Set<String> collectionNames = mongoTemplate.getCollectionNames();
            log.info("当前数据库中的集合: {}", collectionNames);
            // 直接尝试查询，跳过集合存在性检查（可能因权限问题导致检查失败）
            log.info("直接在数据库 {} 中查询集合 {}", currentDb, collectionName);

            // 使用当前数据库进行查询
            MongoUser user = findUserInDatabase(mongoTemplate, yongHuZhangHu, collectionName);

            if (user == null && !"jvs_data".equals(currentDb)) {
                // 如果当前不是jvs_data数据库且没找到用户，尝试切换到jvs_data数据库
                log.info("在当前数据库中未找到用户，尝试切换到jvs_data数据库查找");
                try {
                } catch (Exception e) {
                    log.error("切换到jvs_data数据库失败", e);
                }
            }

            return user;

        } catch (Exception e) {
            log.error("查询用户时发生异常", e);
            return null;
        }
    }

    /**
     * 在指定数据库中查找用户
     */
    private MongoUser findUserInDatabase(org.springframework.data.mongodb.core.MongoTemplate template,
                                        String yongHuZhangHu, String collectionName) {
        try {
            log.info("开始在集合 {} 中查询用户", collectionName);

            // 先尝试查询总数
            long count = 0;
            try {
                count = template.count(new Query(), collectionName);
                log.info("集合 {} 总记录数: {}", collectionName, count);
            } catch (Exception e) {
                log.warn("无法获取集合记录数: {}", e.getMessage());
            }

            // 构建查询
            Query query = new Query();
            query.addCriteria(Criteria.where("yongHuZhangHu").is(yongHuZhangHu));

            log.info("执行查询，条件: yongHuZhangHu = '{}'", yongHuZhangHu);
            MongoUser user = template.findOne(query, MongoUser.class, collectionName);

            if (user != null) {
                log.info("✅ 找到用户: id={}, yongHuXingMing={}", user.getId(), user.getYongHuXingMing());
                return user;
            } else {
                log.warn("❌ 未找到用户，yongHuZhangHu: '{}'", yongHuZhangHu);

                // 尝试查询所有记录的yongHuZhangHu字段，用于调试
                try {
                    Query debugQuery = new Query();
                    debugQuery.fields().include("yongHuZhangHu");
                    debugQuery.limit(5);

                    List<org.bson.Document> allDocs = template.find(debugQuery, org.bson.Document.class, collectionName);
                    log.info("前{}条记录的yongHuZhangHu字段:", Math.min(5, allDocs.size()));
                    for (org.bson.Document doc : allDocs) {
                        Object yongHuZhangHuValue = doc.get("yongHuZhangHu");
                        log.info("  - yongHuZhangHu: '{}' (类型: {})",
                            yongHuZhangHuValue,
                            yongHuZhangHuValue != null ? yongHuZhangHuValue.getClass().getSimpleName() : "null");
                    }
                } catch (Exception debugE) {
                    log.warn("调试查询失败: {}", debugE.getMessage());
                }
            }

            return null;
        } catch (Exception e) {
            log.error("在数据库中查询用户失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean verifyPassword(String inputPassword, String storedPassword) {
        // 这里假设密码是明文存储的，根据您的实际情况调整
        // 如果密码是加密的，需要使用相应的解密或哈希验证方法
        return StringUtils.equals(inputPassword, storedPassword);
    }

    @Override
    public boolean verifyCaptcha(String key,String captcha) {
        // 如果没有提供验证码，则跳过验证
        if (StringUtils.isBlank(captcha)) {
            return true;
        }

        // 如果注入了验证码服务，使用验证码服务验证
        if (captchaService != null) {
            // 这里需要sessionId，实际使用时可能需要从请求中获取
            // 暂时使用固定值，您需要根据实际情况调整
            return captchaService.verifyCaptcha(key, captcha);
        }

        // 简单的验证码验证逻辑（仅用于演示）
        // 实际项目中应该使用更安全的验证机制
        log.warn("使用简单验证码验证逻辑，生产环境请实现完整的验证码服务");
        return "1234".equals(captcha); // 简单示例，实际应该从缓存中获取
    }

    /**
     * 生成登录响应
     */
    private SysUserLoginDTO generateLoginResponse(MongoUser mongoUser) {
        SysUserLoginDTO loginDTO = new SysUserLoginDTO();
        
        // 映射基本信息
        loginDTO.setId(mongoUser.getId());
        loginDTO.setUserName(mongoUser.getYongHuZhangHu());
        loginDTO.setRealName(mongoUser.getYongHuXingMing());
        loginDTO.setDepartId(mongoUser.getBuMenid());
        
        // 解析时间
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (StringUtils.isNotBlank(mongoUser.getCreateTime())) {
                loginDTO.setCreateTime(sdf.parse(mongoUser.getCreateTime()));
            }
            if (StringUtils.isNotBlank(mongoUser.getUpdateTime())) {
                loginDTO.setUpdateTime(sdf.parse(mongoUser.getUpdateTime()));
            }
        } catch (ParseException e) {
            log.warn("时间解析失败: {}", e.getMessage());
        }

        // 设置默认状态为正常
        loginDTO.setState(1);
        
        // 生成Token
        String token = captchaService.createToken(mongoUser.getId(),mongoUser);
        loginDTO.setToken(token);

        // 设置默认角色（可以根据实际需求调整）
        List<String> roles = new ArrayList<>();
        roles.add("user"); // 默认用户角色
        loginDTO.setRoles(roles);
        loginDTO.setRoleIds("user");

        return loginDTO;
    }
}
