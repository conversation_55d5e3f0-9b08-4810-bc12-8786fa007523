package com.kan.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kan.modules.sys.dao.TranspcorpMapper;
import com.kan.modules.sys.dto.TTranspcorpHuaxiang;
import com.kan.modules.sys.service.TranspcorpService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/5/17 16:01
 * @Version: 1.0
 */
@Service
public class TranspcorpServiceImpl implements TranspcorpService {

    @Resource
    TranspcorpMapper transpcorpMapper;
    @Override
    public IPage<TTranspcorpHuaxiang> selectPage(String pageNum, String pageSize) {
        int anInt = Integer.parseInt(pageNum);
        int parseInt = Integer.parseInt(pageSize);
        LambdaQueryWrapper<TTranspcorpHuaxiang> queryWrapper=new LambdaQueryWrapper<>();
        anInt=(anInt - 1) * parseInt;
        IPage<TTranspcorpHuaxiang> page = new Page();
        queryWrapper.orderByDesc(TTranspcorpHuaxiang::getPgsj);
        Integer integer = transpcorpMapper.selectCount(queryWrapper);
        queryWrapper.last("limit " + anInt + "," + parseInt);
        List<TTranspcorpHuaxiang> codeConfigs = transpcorpMapper.selectList(queryWrapper);
        page.setRecords(codeConfigs);
        page.setTotal(integer);
        return page;
    }
}
