

package com.kan.modules.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.kan.modules.sys.entity.SysUserTokenEntity;
import com.kan.common.utils.Result;

/**
 * 用户Token
 *
 * <AUTHOR>
 */
public interface SysUserTokenService extends IService<SysUserTokenEntity> {

	/**
	 * 生成token
	 * @param userId  用户ID
	 */
	Result createToken(long userId);

	/**
	 * 退出，修改token值
	 * @param userId  用户ID
	 */
	void logout(long userId);

}
