package com.kan.modules.sys.service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 验证码服务接口
 */
public interface CaptchaService {

    /**
     * 生成验证码
     * @return 验证码图片的Base64编码
     */
    void generateCaptcha(HttpServletRequest request, HttpServletResponse response);

    /**
     * 验证验证码
     * @param key 会话ID
     * @param captcha 用户输入的验证码
     * @return 是否验证成功
     */
    boolean verifyCaptcha(String key, String captcha);

    /**
     * 清除验证码
     * @param sessionId 会话ID
     */
    void clearCaptcha(String sessionId);
}
