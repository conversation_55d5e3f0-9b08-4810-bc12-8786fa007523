package com.kan.modules.sys.service.impl;

import com.kan.modules.sys.dao.BaseInfoMapper;
import com.kan.modules.sys.dto.BaseInfoDto;
import com.kan.modules.sys.service.BaseInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023/5/17 15:53
 * @Version: 1.0
 */
@Service
public class BaseInfoServiceImpl implements BaseInfoService {
    @Resource
    BaseInfoMapper baseInfoMapper;
    @Override
    public BaseInfoDto selectQuery() {
        BaseInfoDto baseInfoDto = baseInfoMapper.selectList(null).get(0);
        return baseInfoDto;
    }
}
