package com.kan.modules.sys.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kan.common.exception.KanException;
import com.kan.common.utils.EasyUtils;
import com.kan.common.utils.Result;
import com.kan.modules.sys.dao.DrivingInfoMapper;
import com.kan.modules.sys.dao.DrivingInfoNotesMapper;
import com.kan.modules.sys.dto.CakeListResp;
import com.kan.modules.sys.dto.CakeResp;
import com.kan.modules.sys.dto.DrivingInfoData;
import com.kan.modules.sys.dto.DrivingInfoEntity;
import com.kan.modules.sys.entity.TDrivingInfoNotes;
import com.kan.modules.sys.service.DrivingInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-08-30 20:55
 * @Version: 1.0
 */
@Service
public class DrivingInfoServiceImpl implements DrivingInfoService {
    @Resource
    DrivingInfoMapper drivingInfoMapper;
    @Resource
    DrivingInfoNotesMapper drivingInfoNotesMapper;

    @Override
    public IPage<DrivingInfoEntity> getList(DrivingInfoEntity drivingInfoEntity) {
        IPage<DrivingInfoEntity> page = new Page();
        LambdaQueryWrapper<DrivingInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (!ObjectUtils.isEmpty(drivingInfoEntity.getCardNum())) {
            queryWrapper.like(DrivingInfoEntity::getCardNum, drivingInfoEntity.getCardNum());
        }
        if (!ObjectUtils.isEmpty(drivingInfoEntity.getName())) {
            queryWrapper.like(DrivingInfoEntity::getName, drivingInfoEntity.getName());
        }
        if (!ObjectUtils.isEmpty(drivingInfoEntity.getCardStatus())){
            if (drivingInfoEntity.getCardStatus().equals(0)){
                queryWrapper.lt(DrivingInfoEntity::getCardEnd,new Date());
            }else {
                queryWrapper.gt(DrivingInfoEntity::getCardEnd,new Date());
            }
        }
        if (!ObjectUtils.isEmpty(drivingInfoEntity.getDrivingStatus())){
            if (drivingInfoEntity.getDrivingStatus().equals(0)){
                queryWrapper.lt(DrivingInfoEntity::getDrivingEnd,new Date());
            }else {
                queryWrapper.gt(DrivingInfoEntity::getDrivingEnd,new Date());
            }
        }
        if (!ObjectUtils.isEmpty(drivingInfoEntity.getPracticeStatus())){
            if (drivingInfoEntity.getPracticeStatus().equals(0)){
                queryWrapper.lt(DrivingInfoEntity::getPracticeEnd,new Date());
            }else {
                queryWrapper.gt(DrivingInfoEntity::getPracticeEnd,new Date());
            }
        }
        queryWrapper.orderByDesc(DrivingInfoEntity::getCreateTime);
        Integer integer = drivingInfoMapper.selectCount(null);
        if (drivingInfoEntity.getPageNum() != 99999 && drivingInfoEntity.getPageSize() != 99999) {
            drivingInfoEntity.setPageNum((drivingInfoEntity.getPageNum() - 1) * drivingInfoEntity.getPageSize());
            queryWrapper.last("limit " + drivingInfoEntity.getPageNum() + "," + drivingInfoEntity.getPageSize());
        }
        List<DrivingInfoEntity> drivingInfoEntities = drivingInfoMapper.selectList(queryWrapper);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        drivingInfoEntities.forEach(a -> {
            a.setCardStatus(0);
            a.setDrivingStatus(0);
            a.setPracticeStatus(0);
            try {
                if (!ObjectUtils.isEmpty(a.getDrivingEnd()) &&
                        format.parse(a.getDrivingEnd()).before(new Date())) {
                    a.setDrivingStatus(1);
                }
                if (!ObjectUtils.isEmpty(a.getPracticeEnd()) &&
                        format.parse(a.getPracticeEnd()).before(new Date())) {
                    a.setPracticeStatus(1);
                }
                if (!ObjectUtils.isEmpty(a.getCardEnd()) &&
                        format.parse(a.getCardEnd()).before(new Date())) {
                    a.setCardStatus(1);
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
            List<TDrivingInfoNotes> drivingInfoId =
                    drivingInfoNotesMapper.selectList(new QueryWrapper<TDrivingInfoNotes>()
                            .eq("driving_info_id", a.getId()));
            a.setDrivingInfoId(drivingInfoId);
        });
        page.setRecords(drivingInfoEntities);
        page.setTotal(integer);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(DrivingInfoEntity drivingInfoEntity) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        DrivingInfoEntity drivingInfo = drivingInfoMapper.selectOne(new QueryWrapper<DrivingInfoEntity>()
                .eq("card_num", drivingInfoEntity.getCardNum()));
        if (ObjectUtils.isEmpty(drivingInfoEntity.getId())) {
            if (!ObjectUtils.isEmpty(drivingInfo)) {
                throw new KanException("此驾驶员信息已存在!");
            }
            drivingInfoEntity.setCreateTime(new Date());
            drivingInfoMapper.insert(drivingInfoEntity);
            List<TDrivingInfoNotes> drivingInfoId = drivingInfoEntity.getDrivingInfoId();
            if (!ObjectUtils.isEmpty(drivingInfoId)) {
                drivingInfoId.forEach(a -> {
                    if (!ObjectUtils.isEmpty(a.getStartTime()) && !ObjectUtils.isEmpty(a.getEndTime())) {
                        Date parse = null;
                        Date parse1 = null;
                        try {
                            parse = format.parse(a.getStartTime());
                            parse1 = format.parse(a.getEndTime());
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        if (parse.after(parse1)) {
                            throw new KanException("结束时间应大于开始时间！");
                        }
                    }
                    if (ObjectUtils.isEmpty(a.getId())) {
                        a.setDrivingInfoId(drivingInfoEntity.getId());
                        drivingInfoNotesMapper.insert(a);
                    } else {
                        drivingInfoNotesMapper.updateById(a);
                    }
                });
            }
        } else {
            if (!ObjectUtils.isEmpty(drivingInfo)
                    && !drivingInfo.getId().equals(drivingInfoEntity.getId())) {
                throw new KanException("此驾驶员信息已存在!");
            }
            drivingInfoMapper.updateById(drivingInfoEntity);
            List<TDrivingInfoNotes> drivingInfoId = drivingInfoEntity.getDrivingInfoId();
            if (!ObjectUtils.isEmpty(drivingInfoId)) {
                drivingInfoId.forEach(a -> {
                    if (!ObjectUtils.isEmpty(a.getStartTime()) && !ObjectUtils.isEmpty(a.getEndTime())) {
                        Date parse = null;
                        Date parse1 = null;
                        try {
                            parse = format.parse(a.getStartTime());
                            parse1 = format.parse(a.getEndTime());
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        if (parse.after(parse1)) {
                            throw new KanException("结束时间应大于开始时间！");
                        }
                    }
                    if (ObjectUtils.isEmpty(a.getId())) {
                        a.setDrivingInfoId(drivingInfoEntity.getId());
                        drivingInfoNotesMapper.insert(a);
                    } else {
                        drivingInfoNotesMapper.updateById(a);
                    }
                });
            }
        }
        return true;
    }

    @Override
    public void export(HttpServletResponse response, DrivingInfoEntity drivingInfoEntity) {
        try {
            List<DrivingInfoData> dataList = new ArrayList<>();
            String fileName;
            if (!ObjectUtils.isEmpty(drivingInfoEntity.getType())
                    && drivingInfoEntity.getType().equals(1)) {
                DrivingInfoData drivingInfoData = new DrivingInfoData();
                drivingInfoData.setCompany("示例");
                drivingInfoData.setName("示例");
                drivingInfoData.setSex("男");
                drivingInfoData.setCardNum("500000000000000000");
                drivingInfoData.setBirthday("1997-08-29");
                drivingInfoData.setAddress("示例");
                drivingInfoData.setPhone("17600000000");
                drivingInfoData.setDrivingCard("500000000000000000");
                drivingInfoData.setDrivingType("C1");
                drivingInfoData.setDrivingStart("2017-02-22");
                drivingInfoData.setDrivingEnd("2023-02-22");
                drivingInfoData.setPracticeNum("500000000000000000");
                drivingInfoData.setPracticeStart("2016-09-24");
                drivingInfoData.setPracticeEnd("2026-09-24");
                drivingInfoData.setPracticeType("J-货运");
                drivingInfoData.setLaw("无");
                drivingInfoData.setFraction("0");
                drivingInfoData.setResponsibility("无");
                drivingInfoData.setBlackName("无");
                drivingInfoData.setCardStart("2023-06-08");
                drivingInfoData.setCardEnd("2023-05-08");
                drivingInfoData.setCarNum("渝A8888");
                dataList.add(drivingInfoData);
                fileName = URLEncoder.encode("驾驶员导入模板", "UTF-8");
            } else {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                String format1 = format.format(new Date());
                drivingInfoEntity.setPageSize(99999);
                drivingInfoEntity.setPageNum(99999);
                IPage<DrivingInfoEntity> list = getList(drivingInfoEntity);
                List<DrivingInfoEntity> records = list.getRecords();
                records.forEach(a -> {
                    DrivingInfoData drivingInfoData = new DrivingInfoData();
                    BeanUtils.copyProperties(a, drivingInfoData);
                    dataList.add(drivingInfoData);
                });
                fileName = URLEncoder.encode("驾驶员列表", "UTF-8") + format1;
            }
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment  ;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), DrivingInfoData.class)
                    .sheet("驾驶员列表")
                    .doWrite(dataList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Result getUpload(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            return Result.error("导入文件为空！");
        }
        InputStream inputStream = file.getInputStream();
        ExcelReader excelReader = null;
        EasyUtils easyUtils = new EasyUtils(drivingInfoMapper, drivingInfoNotesMapper);
        try {
            excelReader = EasyExcel.read(inputStream, DrivingInfoData.class, easyUtils).build();
            ReadSheet readSheetSix = EasyExcel.readSheet(0).build();
            excelReader.read(readSheetSix);
            easyUtils.getDates().clear();
        } finally {
            if (excelReader != null) {
                // 这里千万别忘记关闭，读的时候会创建临时文件，到时磁盘会崩的
                excelReader.finish();
            }
        }
        return new Result();
    }

    @Override
    public CakeListResp cake() {
        CakeListResp resp = new CakeListResp();
        CakeResp cakeResp = drivingInfoMapper.getCard();
        CakeResp driving = drivingInfoMapper.getDriving();
        CakeResp practice = drivingInfoMapper.getPractice();
        Integer count = drivingInfoMapper.selectCount(null);
        resp.setTotal(count);
        resp.setCard(cakeResp);
        resp.setDriving(driving);
        resp.setPractice(practice);
        return resp;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean delete(DrivingInfoEntity drivingInfoEntity) {
        drivingInfoMapper.deleteById(drivingInfoEntity.getId());
        drivingInfoNotesMapper.delete(new QueryWrapper<TDrivingInfoNotes>()
                .eq("driving_info_id",drivingInfoEntity.getId()));
        return true;
    }
}
