package com.kan.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kan.modules.sys.dao.TdriverMapper;
import com.kan.modules.sys.dto.TDriver;
import com.kan.modules.sys.dto.TransDto;
import com.kan.modules.sys.service.TdriverService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/5/17 16:47
 * @Version: 1.0
 */
@Service
public class TdriverServiceImpl implements TdriverService {
    @Resource
    TdriverMapper tdriverMapper;

    @Override
    public IPage<TDriver> selectPage(String pageNum, String pageSize) {
        int anInt = Integer.parseInt(pageNum);
        int parseInt = Integer.parseInt(pageSize);
        LambdaQueryWrapper<TDriver> queryWrapper=new LambdaQueryWrapper<>();
        anInt=(anInt - 1) * parseInt;
        IPage<TDriver> page = new Page();
        Integer integer = tdriverMapper.selectCount(queryWrapper);
        queryWrapper.last("limit " + anInt + "," + parseInt);
        List<TDriver> codeConfigs = tdriverMapper.selectList(queryWrapper);
        page.setRecords(codeConfigs);
        page.setTotal(integer);
        return page;
    }


}
