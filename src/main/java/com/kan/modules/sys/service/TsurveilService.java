package com.kan.modules.sys.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.kan.modules.sys.dto.CarData;
import com.kan.modules.sys.dto.TDriver;
import com.kan.modules.sys.dto.TSurveil;

import java.util.Map;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023/5/17 16:47
 * @Version: 1.0
 */
public interface TsurveilService {
    IPage<TSurveil> selectPage(Map<String, Object> params);

    TSurveil find(Long tsId);

    IPage<CarData> selectCarData(Map<String, Object> params);
}
