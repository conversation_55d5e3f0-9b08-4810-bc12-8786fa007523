package com.kan.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kan.modules.sys.dao.CarDataMapper;
import com.kan.modules.sys.dao.TsurveilMapper;
import com.kan.modules.sys.dto.CarData;
import com.kan.modules.sys.dto.TDriver;
import com.kan.modules.sys.dto.TSurveil;
import com.kan.modules.sys.service.TsurveilService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/5/17 16:47
 * @Version: 1.0
 */
@Service
public class TsurveilServiceImpl implements TsurveilService {
    @Resource
    TsurveilMapper tsurveilMapper;

    @Override
    public IPage<TSurveil> selectPage(Map<String, Object> params) {
        int anInt = Integer.parseInt(params.get("page").toString());
        int parseInt = Integer.parseInt(params.get("pageSize").toString());
        LambdaQueryWrapper<TSurveil> queryWrapper=new LambdaQueryWrapper<>();
        if (params.containsKey("hphm")) {
            queryWrapper.like(TSurveil::getHphm, params.get("hphm"));
        }
        if (params.containsKey("clbj")) {
            queryWrapper.eq(TSurveil::getClbj, params.get("clbj"));
        }
        anInt=(anInt - 1) * parseInt;
        queryWrapper.orderByDesc(TSurveil::getWfsj);
        IPage<TSurveil> page = new Page();
        Integer integer = tsurveilMapper.selectCount(queryWrapper);
        queryWrapper.last("limit " + anInt + "," + parseInt);
        List<TSurveil> codeConfigs = tsurveilMapper.selectList(queryWrapper);
        page.setRecords(codeConfigs);
        page.setTotal(integer);
        return page;
    }
    @Override
    public TSurveil find(Long tsId) {
        return tsurveilMapper.selectById(tsId);
    }

    @Resource
    CarDataMapper carDataMapper;
    @Override
    public IPage<CarData> selectCarData(Map<String, Object> params) {
        int anInt = Integer.parseInt(params.get("page").toString());
        int parseInt = Integer.parseInt(params.get("pageSize").toString());
        LambdaQueryWrapper<CarData> queryWrapper=new LambdaQueryWrapper<>();
        anInt=(anInt - 1) * parseInt;
        IPage<CarData> page = new Page();
        Integer integer = carDataMapper.selectCount(queryWrapper);
        queryWrapper.last("limit " + anInt + "," + parseInt);
        List<CarData> codeConfigs = carDataMapper.selectList(queryWrapper);
        page.setRecords(codeConfigs);
        page.setTotal(integer);
        return page;
    }
}
