package com.kan.modules.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kan.modules.app.entity.UserEntity;
import com.kan.modules.sys.dao.TransMapper;
import com.kan.modules.sys.dto.TransDto;
import com.kan.modules.sys.service.TransService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/5/17 16:01
 * @Version: 1.0
 */
@Service
public class TransServiceImpl implements TransService {

    @Resource
    TransMapper transMapper;
    @Override
    public IPage<TransDto> selectPage(String pageNum, String pageSize) {
        int anInt = Integer.parseInt(pageNum);
        int parseInt = Integer.parseInt(pageSize);
        LambdaQueryWrapper<TransDto> queryWrapper=new LambdaQueryWrapper<>();
        anInt=(anInt - 1) * parseInt;
        IPage<TransDto> page = new Page();
        Integer integer = transMapper.selectCount(queryWrapper);
        queryWrapper.last("limit " + anInt + "," + parseInt);
        List<TransDto> codeConfigs = transMapper.selectList(queryWrapper);
        page.setRecords(codeConfigs);
        page.setTotal(integer);
        return page;
    }
}
