package com.kan.modules.sys.controller;

import com.kan.common.utils.Result;
import com.kan.modules.sys.service.TsurveilService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/5/17 16:44
 * @Version: 1.0
 */
@RequestMapping("tsurveil")
@RestController
public class TsurveilController {
    @Resource
    TsurveilService tdriverService;
    @GetMapping("page")
    public Result page(@RequestParam Map<String, Object> params){
        return new Result().data(tdriverService.selectPage(params));
    }
    @GetMapping("find")
    public Result find(@RequestParam Long tsId ){
        return new Result().data(tdriverService.find(tsId));
    }

    @GetMapping("carData")
    public Result carData(@RequestParam Map<String, Object> params){
        return new Result().data(tdriverService.selectCarData(params));
    }
}
