package com.kan.modules.sys.controller;

import com.alibaba.fastjson.JSON;
import com.kan.common.utils.Result;
import com.kan.modules.oss.service.FdfsClientService;
import com.kan.modules.sys.dto.IdCardUrlReq;
import okhttp3.*;
import okhttp3.RequestBody;
import org.apache.commons.io.IOUtils;
import org.json.JSONObject;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("baiDu")
public class BaiduApiController {
    public static final String API_KEY = "cqV7MEZZSnx9WFXyhj1P1eKz";
    public static final String SECRET_KEY = "SwOuOVdQ9ljVgCyeDsLEFnaPIrcabggc";

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();
    @Resource
    FdfsClientService fdfsClientService;
    @PostMapping("idCard")
    public Result idCardOcr(@org.springframework.web.bind.annotation.RequestBody IdCardUrlReq req) throws Exception{
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String s = req.getUrl().replaceAll("&amp;", "&");
        String toString = new BASE64Encoder().encode(IOUtils.toByteArray(s));
        String encode = URLEncoder.encode(toString);
        RequestBody body = RequestBody.create(mediaType, "id_card_side=front&image="+encode);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=" + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String string = response.body().string();
//        String s = fdfsClientService.uploadFile(file);
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(string);
        Map<String,Object> result=new HashMap<>();
        result.put("resp",jsonObject);
//        result.put("file",s);
        return new Result().data(result);
    }
    @PostMapping("idCardUrl")
    public Result idCardUrl(@org.springframework.web.bind.annotation.RequestBody IdCardUrlReq req) throws Exception{
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String s = req.getUrl().replaceAll("&amp;", "&");
        String toString = new BASE64Encoder().encode(IOUtils.toByteArray(new URL(s)));
        String encode = URLEncoder.encode(toString);
        return baseUploadOcr(mediaType,encode,"idcard");
    }

    @PostMapping("carNoUrl")
    public Result carNoUrl(@org.springframework.web.bind.annotation.RequestBody IdCardUrlReq req) throws Exception{
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String s = req.getUrl().replaceAll("&amp;", "&");
        String toString = new BASE64Encoder().encode(IOUtils.toByteArray(new URL(s)));
        String encode = URLEncoder.encode(toString);
        return baseUploadOcr(mediaType,encode,"license_plate");
    }

    @PostMapping("congye")
    public Result getList(MultipartFile file)throws Exception{
        Map<String,String> result=new HashMap<>();
        result.put("num","51021219820309387X");
        result.put("end","2026-09-24");
        result.put("kaoheriqi","03月02日");
        result.put("type","J-货运");
        String s = fdfsClientService.uploadFile(file);
        result.put("file",s);
        return new Result().data(result);
    }
    @PostMapping("drivingCard")
    public Result drivingCard(MultipartFile file) throws Exception{
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String toString = new BASE64Encoder().encode(file.getBytes());
        String encode = URLEncoder.encode(toString);
        RequestBody body = RequestBody.create(mediaType, "id_card_side=front&image="+encode);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/driving_license?access_token=" + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String string = response.body().string();
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(string);
        Map<String,Object> result=new HashMap<>();
        result.put("resp",jsonObject);
        return new Result().data(result);
    }
    @PostMapping("drivingCardUrl")
    public Result drivingCardUrl(@org.springframework.web.bind.annotation.RequestBody IdCardUrlReq req) throws Exception{
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String s = req.getUrl().replaceAll("&amp;", "&");
        String toString = new BASE64Encoder().encode(IOUtils.toByteArray(new URL(s)));
        String encode = URLEncoder.encode(toString);
        return baseUploadOcr(mediaType,encode,"driving_license");
    }


    private Result baseUploadOcr(MediaType mediaType, String encode,String type) throws IOException {
        RequestBody body = RequestBody.create(mediaType, "id_card_side=front&image="+encode);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/ocr/v1/"+type+"?access_token=" + getAccessToken())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .addHeader("charset","UTF-8")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String string = response.body().string();
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(string);
        Map<String,Object> result=new HashMap<>();
        result.put("resp",jsonObject);
        return new Result().data(result);
    }
    /**
     * imgFile 图片本地存储路径
     */
    public static String getImgFileToBase64(String imgFile) {
        //将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        InputStream inputStream = null;
        byte[] buffer = null;
        //读取图片字节数组
        try {
            inputStream = new FileInputStream(imgFile);
            int count = 0;
            while (count == 0) {
                count = inputStream.available();
            }
            buffer = new byte[count];
            inputStream.read(buffer);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    // 关闭inputStream流
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        // 对字节数组Base64编码
        return new BASE64Encoder().encode(buffer);
    }


    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    static String getAccessToken() throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + API_KEY
                + "&client_secret=" + SECRET_KEY);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        return new JSONObject(response.body().string()).getString("access_token");
    }
    
}