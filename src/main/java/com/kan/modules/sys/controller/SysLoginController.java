

package com.kan.modules.sys.controller;

import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.kan.common.exception.KanException;
import com.kan.modules.oss.entity.OssVo;
import com.kan.modules.sys.entity.SysUserEntity;
import com.kan.modules.sys.form.SysLoginForm;
import com.kan.modules.sys.service.SysCaptchaService;
import com.kan.common.utils.Result;
import com.kan.modules.sys.service.SysUserService;
import com.kan.modules.sys.service.SysUserTokenService;
import org.apache.commons.io.IOUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Map;

/**
 * 登录相关
 *
 * @<NAME_EMAIL>
 */
@RestController
public class SysLoginController extends AbstractController {
	@Value("${oss.accessKeyId}")
	private String accessKeyId;

	@Value("${oss.accessKeySecret}")
	private String accessKeySecret;

	@Value("${oss.roleArn}")
	private String roleArn;

	@Value("${oss.region}")
	private String region;

	@Value("${oss.bucket}")
	private String bucket;
	@Value("${oss.endpoint}")
	private String endpoint;

	@Autowired
	private SysUserService sysUserService;
	@Autowired
	private SysUserTokenService sysUserTokenService;
	@Autowired
	private SysCaptchaService sysCaptchaService;

	/**
	 * 验证码
	 */
	@GetMapping("captcha.jpg")
	public void captcha(HttpServletResponse response, String uuid)throws IOException {
		response.setHeader("Cache-Control", "no-store, no-cache");
		response.setContentType("image/jpeg");

		//获取图片验证码
		BufferedImage image = sysCaptchaService.getCaptcha(uuid);

		ServletOutputStream out = response.getOutputStream();
		ImageIO.write(image, "jpg", out);
		IOUtils.closeQuietly(out);
	}
	@GetMapping(value = "getToken")
	@ResponseBody
	public Result getToken() {
		String roleSessionName = "session-name";// 自定义即可
		// 定制你的policy
		String policy ="{\n" +
				"    \"Statement\": [\n" +
				"        {\n" +
				"            \"Action\": \"oss:*\",\n" +
				"            \"Effect\": \"Allow\",\n" +
				"            \"Resource\": \"*\"\n" +
				"        }\n" +
				"    ],\n" +
				"    \"Version\": \"1\"\n" +
				"}";
		OssVo ossVo=new OssVo();

		ossVo.setBucket(bucket);
		ossVo.setRegion(region);
		try {
			AssumeRoleResponse response = OssVo.assumeRole(accessKeyId, accessKeySecret, roleArn, roleSessionName,
					policy);
			ossVo.setSecurityToken(response.getCredentials().getSecurityToken());
			ossVo.setAccessKeyId(response.getCredentials().getAccessKeyId());
			ossVo.setAccessKeySecret(response.getCredentials().getAccessKeySecret());
		} catch (ClientException e) {
			throw new KanException("Error message: " + e.getErrMsg());
		}

		return  new Result().data(ossVo);
	}

	/**
	 * 登录
	 */
	@PostMapping("/sys/login")
	public Map<String, Object> login(@RequestBody SysLoginForm form)throws IOException {
		boolean captcha = sysCaptchaService.validate(form.getUuid(), form.getCaptcha());
		if(!captcha){
			return Result.error("验证码不正确");
		}

		//用户信息
		SysUserEntity user = sysUserService.queryByUserName(form.getUsername());

		//账号不存在、密码错误
		if(user == null || !user.getPassword().equals(new Sha256Hash(form.getPassword(), user.getSalt()).toHex())) {
			return Result.error("账号或密码不正确");
		}

		//账号锁定
		if(user.getStatus() == 0){
			return Result.error("账号已被锁定,请联系管理员");
		}

		//生成token，并保存到数据库
		Result r = sysUserTokenService.createToken(user.getUserId());
		return r;
	}


	/**
	 * 退出
	 */
	@PostMapping("/sys/logout")
	public Result logout() {
		sysUserTokenService.logout(getUserId());
		return Result.ok();
	}
	
}
