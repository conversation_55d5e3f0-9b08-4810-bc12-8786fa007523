package com.kan.modules.sys.controller;

import com.kan.common.utils.Result;
import com.kan.modules.sys.service.AcdService;
import com.kan.modules.sys.service.TdriverService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023/5/17 16:53
 * @Version: 1.0
 */
@RequestMapping("tacd")
@RestController
public class AcdController {
    @Resource
    AcdService acdService;
    @GetMapping("page")
    public Result page(@RequestParam String page , @RequestParam String pageSize){
        return new Result().data(acdService.selectPage(page,pageSize));
    }


}

