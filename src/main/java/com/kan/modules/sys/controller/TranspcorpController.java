package com.kan.modules.sys.controller;

import com.kan.common.utils.Result;
import com.kan.modules.sys.service.TranspcorpService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: <PERSON><PERSON>yi<PERSON>
 * @Date: Created in 2023/5/17 16:00
 * @Version: 1.0
 */
@RestController
@RequestMapping("ttranspcorphuaxiang")
public class TranspcorpController {
    @Resource
    TranspcorpService transService;

    @GetMapping("page")
    public Result page(@RequestParam String page ,@RequestParam String pageSize) {
        return new Result().data(transService.selectPage(page,pageSize));
    }
}
