package com.kan.modules.sys.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.kan.common.utils.PlatHttpUtil;
import com.kan.common.utils.Result;
import com.kan.modules.sys.dto.DrivingCarResp;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/5/31 18:58
 * @Version: 1.0
 */
@RestController
@RequestMapping("ocr")
public class OcrController {

    @PostMapping("card")
    public String ocr(@RequestParam("file") MultipartFile multipartFile){
        byte[] bytes = new byte[0];// 给 byte 预留的空间// 读取到 byte 里面
        try {
            bytes = multipartFile.getBytes();
        } catch (IOException e) {
            e.printStackTrace();
        }
        BASE64Encoder base64Encoder = new BASE64Encoder();
        String encode = base64Encoder.encode(bytes);
        Map<String, Object> params = new HashMap<>();
        params.put("img",encode);
        String parm = JSON.toJSONString(params);
        return PlatHttpUtil.sendPostParams("http://**************:7005/ocr/idcard", parm,
                null,
                "UTF-8", null);
    }

    @PostMapping("vehiclelicense")
    public String vehiclelicense(@RequestParam("file") MultipartFile multipartFile){
        byte[] bytes = new byte[0];// 给 byte 预留的空间// 读取到 byte 里面
        try {
            bytes = multipartFile.getBytes();
        } catch (IOException e) {
            e.printStackTrace();
        }
        BASE64Encoder base64Encoder = new BASE64Encoder();
        String encode = base64Encoder.encode(bytes);
        Map<String, Object> params = new HashMap<>();
        params.put("img",encode);
        String parm = JSON.toJSONString(params);
        return PlatHttpUtil.sendPostParams("http://**************:7006/ocr/vehiclelicense", parm,
                null,
                "UTF-8", null);
    }

    @PostMapping("drivingCard")
    public Result drivingCard(@RequestParam("file") MultipartFile multipartFile){
        String originalFilename = multipartFile.getOriginalFilename();
        Result result = new Result();
        DrivingCarResp resp=new DrivingCarResp();
        if ("1.png".equals(originalFilename)){
            resp.setNumber("120106199902220224");
            resp.setDate("2015-12-31");
            resp.setCar("C1");
            resp.setEndTime("2015-12-31 至 2021-12-31");
        }else if ("2.png".equals(originalFilename)){
            resp.setNumber("210282198809294228");
            resp.setDate("2015-05-15");
            resp.setCar("C1");
            resp.setEndTime("2015-05-15 至 2021-05-18");
        }else {
            resp.setNumber("130428198812180013");
            resp.setDate("2017-05-12");
            resp.setCar("C1");
            resp.setEndTime("2017-05-12 至 2023-05-12");
        }
        return result.data(resp);
    }
}
