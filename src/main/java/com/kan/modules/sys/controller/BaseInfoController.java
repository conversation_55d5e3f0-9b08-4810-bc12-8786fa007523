package com.kan.modules.sys.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kan.common.utils.PlatHttpUtil;
import com.kan.common.utils.Result;
import com.kan.modules.sys.dao.DrivingInfoMapper;
import com.kan.modules.sys.dto.MessageTemplateEntity;
import com.kan.modules.sys.dto.MessageValueEntity;
import com.kan.modules.sys.dto.OpenTemp;
import com.kan.modules.sys.dto.TSurveil;
import com.kan.modules.sys.service.BaseInfoService;
import com.kan.modules.sys.service.TsurveilService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/5/17 15:52
 * @Version: 1.0
 */
@RestController
@RequestMapping("tbaseinfo")
public class BaseInfoController {
    @Resource
    BaseInfoService baseInfoService;
    @Resource
    TsurveilService tdriverService;
    @Resource
    DrivingInfoMapper drivingInfoMapper;

    @GetMapping("query")
    public Result query() {
        return new Result().data(baseInfoService.selectQuery());
    }

    @GetMapping("send")
    private Result sendMessage(@RequestParam Long tsId) {
        TSurveil tSurveil = tdriverService.find(tsId);
        String hphm = tSurveil.getHphm();
        String wfsj = tSurveil.getWfsj();
        String bz = "车主你好，你有新的违章，请尽快处理";
        MessageTemplateEntity messageTemplateEntity = new MessageTemplateEntity();
        messageTemplateEntity.setMessageData(
                new MessageValueEntity(hphm),
                new MessageValueEntity(wfsj),
                new MessageValueEntity(bz)
        );
        OpenTemp openTemp = drivingInfoMapper.getOpenId();
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("touser", openTemp.getOpenid());
        paramsMap.put("template_id", openTemp.getTempid());
        paramsMap.put("data", messageTemplateEntity);
        String parm = JSON.toJSONString(paramsMap);
        String userAccessToken = PlatHttpUtil.sendGetParams("https://api.weixin.qq.com/cgi-bin/token?" +
                "grant_type=client_credential" +
                "&appid=" + openTemp.getAppid() +
                "&secret=" + openTemp.getSecret(), null, "UTF-8",null);
        JSONObject jsonObject = JSONObject.parseObject(userAccessToken);
        String accessToken = jsonObject.get("access_token").toString();
        //发送请求路径拼接获取到的access_token
        String s = PlatHttpUtil.sendPostParams("https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token" +
                        "=" + accessToken,
                parm, null, "UTF-8", null);
        String errcode = JSONObject.parseObject(s).get("errcode").toString();
        if ("0".equals(errcode)) {
            return Result.ok("推送成功");
        } else {
            return Result.error("推送失败");
        }
    }
}
