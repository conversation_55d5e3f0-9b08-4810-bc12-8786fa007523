package com.kan.modules.sys.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.kan.common.utils.JvsUtils;
import com.kan.common.utils.Result;
import com.kan.modules.app.annotation.Login;
import com.kan.modules.sys.dto.DrivingInfoEntity;
import com.kan.modules.sys.service.DrivingInfoService;
import io.swagger.annotations.ApiOperation;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-08-30 20:54
 * @Version: 1.0
 */
@RestController
@RequestMapping("driving")
public class DrivingInfoController {
    @Resource
    DrivingInfoService drivingInfoService;

    @PostMapping("list")
    public Result getList(@RequestBody DrivingInfoEntity drivingInfoEntity){
        return new Result().data(drivingInfoService.getList(drivingInfoEntity));
    }

    @PostMapping("add")
    @ApiOperation(value = "add" , notes = "新增企业基本信息")
    public Result add(@RequestBody DrivingInfoEntity drivingInfoEntity){
        Boolean status = drivingInfoService.add(drivingInfoEntity);
        if (!status){
            return Result.addError();
        }
        return new Result();
    }
    @PostMapping("export")
    public void export(HttpServletResponse response,@RequestBody DrivingInfoEntity drivingInfoEntity) {
        drivingInfoService.export(response,drivingInfoEntity);
    }
    @PostMapping("delete")
    public Result delete(@RequestBody DrivingInfoEntity drivingInfoEntity) {
        Boolean status = drivingInfoService.delete(drivingInfoEntity);
        if (!status){
            return Result.deleteError();
        }
        return new Result();
    }
    @PostMapping("upload")
    @ApiOperation("导入督办任务")
    public Result uploadSupervise(@RequestParam("file") MultipartFile file) throws IOException {
        //导入督办
        return drivingInfoService.getUpload(file);
    }
    @PostMapping("cake")
    public Result cake() {
        return new Result().data(drivingInfoService.cake());
    }

    public static void main(String[] args) {
        System.out.println(JvsUtils.sendPostValidateUser("wangchen", "13014d713abf32c5deb61d11252b6558"));
    }
}
