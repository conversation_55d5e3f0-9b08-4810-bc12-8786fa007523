package com.kan.modules.sys.controller;


import com.kan.modules.app.utils.UEditorFile;
import com.kan.modules.app.utils.UEditorUpload;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@RestController
@CrossOrigin
public class UEditorController {

    @Autowired
    private UEditorUpload uEditorUpload;

    @RequestMapping("/config")
    public String exec(HttpServletRequest request,
                       HttpServletResponse response,
                       @RequestParam(value = "action") String action,
                       @RequestParam(value = "upfile", required = false) MultipartFile upfile) throws Exception {
        if ("config".equals(action)) {
            request.setCharacterEncoding("utf-8");
            response.setContentType("text/html");
//            String rootPath = request.getSession().getServletContext().getRealPath("/");
            String callbackName = request.getParameter("callback");
//            String newExce = new ActionEnter(request, rootPath).exec();
            String exec=callbackName+"({\"videoMaxSize\":102400000,\"videoActionName\":\"uploadvideo\",\"fileActionName\":\"uploadfile\",\"fileManagerListPath\":\"/ueditor/jsp/upload/file/\",\"imageCompressBorder\":1600,\"imageManagerAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\"],\"imageManagerListPath\":\"/ueditor/jsp/upload/image/\",\"fileMaxSize\":51200000,\"fileManagerAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\",\".flv\",\".swf\",\".mkv\",\".avi\",\".rm\",\".rmvb\",\".mpeg\",\".mpg\",\".ogg\",\".ogv\",\".mov\",\".wmv\",\".mp4\",\".webm\",\".mp3\",\".wav\",\".mid\",\".rar\",\".zip\",\".tar\",\".gz\",\".7z\",\".bz2\",\".cab\",\".iso\",\".doc\",\".docx\",\".xls\",\".xlsx\",\".ppt\",\".pptx\",\".pdf\",\".txt\",\".md\",\".xml\"],\"fileManagerActionName\":\"listfile\",\"snapscreenInsertAlign\":\"none\",\"scrawlActionName\":\"uploadscrawl\",\"videoFieldName\":\"upfile\",\"imageCompressEnable\":true,\"videoUrlPrefix\":\"\",\"fileManagerUrlPrefix\":\"\",\"catcherAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\"],\"imageManagerActionName\":\"listimage\",\"snapscreenPathFormat\":\"/ueditor/jsp/upload/image/{yyyy}{mm}{dd}/{time}{rand:6}\",\"scrawlPathFormat\":\"/ueditor/jsp/upload/image/{yyyy}{mm}{dd}/{time}{rand:6}\",\"scrawlMaxSize\":2048000,\"imageInsertAlign\":\"none\",\"catcherPathFormat\":\"/ueditor/jsp/upload/image/{yyyy}{mm}{dd}/{time}{rand:6}\",\"catcherMaxSize\":2048000,\"snapscreenUrlPrefix\":\"\",\"imagePathFormat\":\"/image/upload/ueditor/{time}/{filename}\",\"imageManagerUrlPrefix\":\"\",\"scrawlUrlPrefix\":\"\",\"scrawlFieldName\":\"upfile\",\"imageMaxSize\":2048000,\"imageAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\"],\"snapscreenActionName\":\"uploadimage\",\"catcherActionName\":\"catchimage\",\"fileFieldName\":\"upfile\",\"fileUrlPrefix\":\"\",\"imageManagerInsertAlign\":\"none\",\"catcherLocalDomain\":[\"127.0.0.1\",\"localhost\",\"img.baidu.com\"],\"filePathFormat\":\"/ueditor/jsp/upload/file/{yyyy}{mm}{dd}/{time}{rand:6}\",\"videoPathFormat\":\"/ueditor/jsp/upload/video/{yyyy}{mm}{dd}/{time}{rand:6}\",\"fileManagerListSize\":20,\"imageActionName\":\"uploadimage\",\"imageFieldName\":\"upfile\",\"imageUrlPrefix\":\"\",\"scrawlInsertAlign\":\"none\",\"fileAllowFiles\":[\".png\",\".jpg\",\".jpeg\",\".gif\",\".bmp\",\".flv\",\".swf\",\".mkv\",\".avi\",\".rm\",\".rmvb\",\".mpeg\",\".mpg\",\".ogg\",\".ogv\",\".mov\",\".wmv\",\".mp4\",\".webm\",\".mp3\",\".wav\",\".mid\",\".rar\",\".zip\",\".tar\",\".gz\",\".7z\",\".bz2\",\".cab\",\".iso\",\".doc\",\".docx\",\".xls\",\".xlsx\",\".ppt\",\".pptx\",\".pdf\",\".txt\",\".md\",\".xml\"],\"catcherUrlPrefix\":\"\",\"imageManagerListSize\":20,\"catcherFieldName\":\"source\",\"videoAllowFiles\":[\".flv\",\".swf\",\".mkv\",\".avi\",\".rm\",\".rmvb\",\".mpeg\",\".mpg\",\".ogg\",\".ogv\",\".mov\",\".wmv\",\".mp4\",\".webm\",\".mp3\",\".wav\",\".mid\"]});\n";
            System.out.println(exec);
            return exec;
        } else if ("uploadimage".equals(action)) {
            UEditorFile uEditorFile = uEditorUpload.uploadImage(upfile);
            JSONObject jsonObject = new JSONObject(uEditorFile);
            return jsonObject.toString();
        }
        return "无效Action";
    }

}
