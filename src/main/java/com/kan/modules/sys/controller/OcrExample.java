package com.kan.modules.sys.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.kan.common.utils.PlatHttpUtil;
import net.sourceforge.tess4j.*;
import sun.misc.BASE64Encoder;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class OcrExample {
//    public static void main(String[] args) {
//        File imageFile = new File("C:/Users/<USER>/Desktop/1.png");
//        ITesseract instance = new Tesseract();
//        instance.setDatapath("D:/OCR/Tesseract-OCR/tessdata");
//        try {
//            String result = instance.doOCR(imageFile);
//            System.out.println(result);
//        } catch (TesseractException e) {
//            System.err.println(e.getMessage());
//        }
//    }
    public static void main(String[] args) throws IOException {
        File imageFile = new File("C:/Users/<USER>/Desktop/2.png");
        FileInputStream fileInputStream = new FileInputStream(imageFile);
        byte [] bytes = new byte[fileInputStream.available()]; // 给 byte 预留的空间
        fileInputStream.read(bytes); // 读取到 byte 里面
        BASE64Encoder base64Encoder = new BASE64Encoder();
        String encode = base64Encoder.encode(bytes);
        Map<String, Object> params = new HashMap<>();
        params.put("img",encode);
        String parm = JSON.toJSONString(params);
        String ss= PlatHttpUtil.sendPostParams("http://139.186.77.127:7005/ocr/idcard", parm,
                null,
                "UTF-8", null);
        Map<String, String> jsonMap = JSON.parseObject(ss, new TypeReference<HashMap<String, String>>() {});
        System.out.println(jsonMap);
    }
}