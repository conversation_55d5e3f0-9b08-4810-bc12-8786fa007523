package com.kan.modules.sys.controller;

import com.kan.common.utils.Result;
import com.kan.core.api.ApiRest;
import com.kan.core.api.controller.BaseController;
import com.kan.modules.sys.dto.MongoUser;
import com.kan.modules.sys.dto.SysUserLoginDTO;
import com.kan.modules.sys.dto.SysUserLoginReqDTO;
import com.kan.modules.sys.service.CaptchaService;
import com.kan.modules.sys.service.MongoUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 验证码控制器
 */
@Api(value = "验证码管理", tags = "验证码管理")
@RestController
@RequestMapping("/exam/api/sys/user")
public class CaptchaController extends BaseController {

    @Autowired(required = false)
    private CaptchaService captchaService;
    @Autowired
    private MongoUserService mongoUserService;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 获取验证码
     */
    @CrossOrigin
    @ApiOperation(value = "获取验证码", notes = "获取图形验证码")
    @RequestMapping(value = "/get-captcha", method = {RequestMethod.GET, RequestMethod.POST})
    public void captcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        captchaService.generateCaptcha(request, response);
    }
    /**
     * MongoDB用户登录
     * @param reqDTO 登录请求参数
     * @return 登录响应
     */
    @CrossOrigin
    @ApiOperation(value = "MongoDB用户登录", notes = "基于MongoDB存储的用户登录，支持验证码验证")
    @RequestMapping(value = "/mongo-login", method = {RequestMethod.POST})
    public Result mongoLogin(@RequestBody SysUserLoginReqDTO reqDTO) {
        SysUserLoginDTO respDTO = mongoUserService.login(reqDTO.getUsername(), reqDTO.getPassword(), reqDTO.getCaptcha(), reqDTO.getKey());
        return new Result().appOk(respDTO);
    }
    /**
     * 测试MongoDB用户查询
     * @param username 用户账户
     * @return 用户信息
     */
    @CrossOrigin
    @ApiOperation(value = "测试MongoDB用户查询", notes = "用于调试MongoDB用户查询问题")
    @RequestMapping(value = "/test-mongo-user", method = {RequestMethod.GET})
    public ApiRest testMongoUser(@RequestParam String username) {
        try {
            MongoUser user = mongoUserService.findByYongHuZhangHu(username);
            if (user != null) {
                return super.success(user);
            } else {
                return super.failure("未找到用户: " + username);
            }
        } catch (Exception e) {
            return super.failure("查询异常: " + e.getMessage());
        }
    }
}
