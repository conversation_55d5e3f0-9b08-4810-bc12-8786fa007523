package com.kan.modules.sys.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kan.common.utils.QueryRequest;
import com.kan.modules.sys.entity.TDrivingInfoNotes;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-08-29 19:37
 * @Version: 1.0
 */
@Data
@TableName("t_driving_info")
public class DrivingInfoEntity extends QueryRequest {
    public Long id;
    public String company;
    public String name;
    public String sex;
    public String birthday;
    public String address;
    public String phone;
    public String drivingCard;
    public String drivingType;
    public String drivingStart;
    public String drivingEnd;
    public String practiceNum;
    public String practiceStart;
    public String practiceEnd;
    public String practiceType;
    public String law;
    public String fraction;
    public String responsibility;
    public String blackName;
    public Date createTime;
    public String carNum;
    public String idCardImage;
    public String cardBackImage;
    public String drivingImage;
    public String photoImage;
    public String practiceImage;
    public String cardNum;
    public String cardStart;
    public String cardEnd;
    //简历信息
    @TableField(exist = false)
    public List<TDrivingInfoNotes> drivingInfoId;
    //身份证
    @TableField(exist = false)
    public MultipartFile idCardImageFile;
    //身份证是否过期
    @TableField(exist = false)
    public Integer cardStatus;
    //驾驶证是否过期
    @TableField(exist = false)
    public Integer drivingStatus;
    //从业资格证是否过期
    @TableField(exist = false)
    public Integer practiceStatus;
    //驾驶证
    @TableField(exist = false)
    public MultipartFile drivingImageFile;
    //从业资格证
    @TableField(exist = false)
    public MultipartFile practiceImageFile;
    //照片
    @TableField(exist = false)
    public MultipartFile photoImageFile;
    //照片
    @TableField(exist = false)
    public Integer type;

}
