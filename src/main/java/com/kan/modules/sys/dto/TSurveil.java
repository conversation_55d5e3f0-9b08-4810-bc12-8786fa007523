package com.kan.modules.sys.dto;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_surveil")
public class TSurveil {

  @TableId
  private Long tsId;
  private String wfms;
  private String fkje;
  private String wfjfs;
  private String wfzt;
  private String xh;
  private String hpzl;
  private String hphm;
  private String fzjg;
  private String wfdz;
  private String wfxw;
  private String clbj;
  private String jkbj;
  private String cjjg;
  private String cjjgmc;
  private String fsjg;
  private String jsjg;
  private String gxsj;
  private String dcbj;
  private String wfsj;
  private String clsj;
  private String clbjStr;
  private String jkbjStr;
  private String cljg;
  private String cljgmc;
  private String cljg1;
  private String cljg2;
  private String wrapStr;
  private String wrapCljg;
  private String hpzlStr;
  private String fzjgStr;
  private String sfjf;
  private String sfcl;
  private String photos;
  private String photosLocal;
  private String wfxcsq;
  private String sfyss;
  private String scz;
  private String bzz;
  private String swjgbj;
  private String glbm;
  private String jdsbh;
  private long updateTime;
}
