package com.kan.modules.sys.dto;

import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * MongoDB用户实体类
 * 对应表名：444d5fec7d57c38f8e270f028acfca9748d469
 */
@Data
@Document(collection = "444d5fec7d57c38f8e270f028acfca9748d469")
public class MongoUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @MongoId
    private ObjectId _id;

    @Field("yongHuXingMing")
    private String yongHuXingMing; // 用户姓名

    @Field("yongHuShouJiHao")
    private String yongHuShouJiHao; // 用户手机号

    @Field("yongHuZhangHu")
    private String yongHuZhangHu; // 用户账户

    @Field("miMa")
    private String miMa; // 密码

    @Field("yongHuLaiYuan")
    private String yongHuLaiYuan; // 用户来源

    @Field("dataId")
    private String dataId;

    @Field("id")
    private String id;

    @Field("modelId")
    private String modelId;

    @Field("updateTime")
    private String updateTime; // 更新时间

    @Field("updateBy")
    private String updateBy; // 更新人

    @Field("updateById")
    private String updateById; // 更新人ID

    @Field("delFlag")
    private Boolean delFlag; // 删除标志

    @Field("jobId")
    private String jobId;

    @Field("deptId")
    private List<String> deptId; // 部门ID列表

    @Field("createById")
    private String createById; // 创建人ID

    @Field("createBy")
    private String createBy; // 创建人

    @Field("createTime")
    private String createTime; // 创建时间

    @Field("buMenid")
    private String buMenid; // 部门ID

    @Field("yongHuid")
    private String yongHuid; // 用户ID
}
