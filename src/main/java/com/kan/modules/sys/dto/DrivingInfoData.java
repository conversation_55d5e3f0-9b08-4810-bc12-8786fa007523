package com.kan.modules.sys.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-08-31 14:20
 * @Version: 1.0
 */
@Getter
@Setter
@HeadRowHeight(40)
@ContentRowHeight(30)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND)
@EqualsAndHashCode
public class DrivingInfoData {
    @ColumnWidth(13)
    @ExcelProperty({"单位"})
    public String company;
    @ColumnWidth(13)
    @ExcelProperty({"姓名"})
    public String name;
    @ColumnWidth(13)
    @ExcelProperty({"性别"})
    public String sex;
    @ColumnWidth(13)
    @ExcelProperty({"身份证号"})
    public String cardNum;
    @ColumnWidth(13)
    @ExcelProperty({"出生日期"})
    public String birthday;
    @ColumnWidth(13)
    @ExcelProperty({"地址"})
    public String address;
    @ColumnWidth(13)
    @ExcelProperty({"联系电话"})
    public String phone;
    @ColumnWidth(13)
    @ExcelProperty({"驾驶证号"})
    public String drivingCard;
    @ColumnWidth(13)
    @ExcelProperty({"准驾车型"})
    public String drivingType;
    @ColumnWidth(16)
    @ExcelProperty({"初次领证时间"})
    public String drivingStart;
    @ColumnWidth(16)
    @ExcelProperty({"驾驶证有效期"})
    public String drivingEnd;
    @ColumnWidth(16)
    @ExcelProperty({"从业资格证号"})
    public String practiceNum;
    @ColumnWidth(20)
    @ExcelProperty({"从业资格证初次领证时间"})
    public String practiceStart;
    @ColumnWidth(18)
    @ExcelProperty({"从业资格证有效期"})
    public String practiceEnd;
    @ColumnWidth(18)
    @ExcelProperty({"从业资格证类型"})
    public String practiceType;
    @ColumnWidth(18)
    @ExcelProperty({"驾驶人违法信息查询"})
    public String law;
    @ColumnWidth(13)
    @ExcelProperty({"记分查询"})
    public String fraction;
    @ColumnWidth(20)
    @ExcelProperty({"死亡事故同等以上责任查询"})
    public String responsibility;
    @ColumnWidth(20)
    @ExcelProperty({"交运集团驾驶人黑名单查询"})
    public String blackName;
    @ColumnWidth(20)
    @ExcelProperty({"身份证领证时间"})
    public String cardStart;
    @ColumnWidth(20)
    @ExcelProperty({"身份证有效期"})
    public String cardEnd;
    @ColumnWidth(20)
    @ExcelProperty({"拟驾车牌号"})
    public String carNum;
}
