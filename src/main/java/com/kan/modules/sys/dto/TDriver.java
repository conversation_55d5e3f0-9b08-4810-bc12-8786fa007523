package com.kan.modules.sys.dto;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_driver")
public class TDriver {

  @TableId
  private Long tdrId;
  private String sfzmhm;
  private String xm;
  private String zt;
  private String ztjb;
  private String syrq;
  private String syyxqz;
  private String yxqz;
  private String dbywlx;
  private String ljjf;
  private String dbywlxStr;
  private String ztStr;
  private String xxsl;
  private String zdyjsl;
  private String dabh;
  private String wclsl;
  private String gzsl;
  private String cgsl;
  private String qybh;
  private String sfsy;
  private String jmsfzmhm;
  private String xh;
  private String fzjg;
  private String qyfzjg;
  private String sfyd;
  private String qfrq;
  private String cyzgzh;
  private String zjcx;
  private long updateTime;


  public long getTdrId() {
    return tdrId;
  }

  public void setTdrId(long tdrId) {
    this.tdrId = tdrId;
  }


  public String getSfzmhm() {
    return sfzmhm;
  }

  public void setSfzmhm(String sfzmhm) {
    this.sfzmhm = sfzmhm;
  }


  public String getXm() {
    return xm;
  }

  public void setXm(String xm) {
    this.xm = xm;
  }


  public String getZt() {
    return zt;
  }

  public void setZt(String zt) {
    this.zt = zt;
  }


  public String getZtjb() {
    return ztjb;
  }

  public void setZtjb(String ztjb) {
    this.ztjb = ztjb;
  }


  public String getSyrq() {
    return syrq;
  }

  public void setSyrq(String syrq) {
    this.syrq = syrq;
  }


  public String getSyyxqz() {
    return syyxqz;
  }

  public void setSyyxqz(String syyxqz) {
    this.syyxqz = syyxqz;
  }


  public String getYxqz() {
    return yxqz;
  }

  public void setYxqz(String yxqz) {
    this.yxqz = yxqz;
  }


  public String getDbywlx() {
    return dbywlx;
  }

  public void setDbywlx(String dbywlx) {
    this.dbywlx = dbywlx;
  }


  public String getLjjf() {
    return ljjf;
  }

  public void setLjjf(String ljjf) {
    this.ljjf = ljjf;
  }


  public String getDbywlxStr() {
    return dbywlxStr;
  }

  public void setDbywlxStr(String dbywlxStr) {
    this.dbywlxStr = dbywlxStr;
  }


  public String getZtStr() {
    return ztStr;
  }

  public void setZtStr(String ztStr) {
    this.ztStr = ztStr;
  }


  public String getXxsl() {
    return xxsl;
  }

  public void setXxsl(String xxsl) {
    this.xxsl = xxsl;
  }


  public String getZdyjsl() {
    return zdyjsl;
  }

  public void setZdyjsl(String zdyjsl) {
    this.zdyjsl = zdyjsl;
  }


  public String getDabh() {
    return dabh;
  }

  public void setDabh(String dabh) {
    this.dabh = dabh;
  }


  public String getWclsl() {
    return wclsl;
  }

  public void setWclsl(String wclsl) {
    this.wclsl = wclsl;
  }


  public String getGzsl() {
    return gzsl;
  }

  public void setGzsl(String gzsl) {
    this.gzsl = gzsl;
  }


  public String getCgsl() {
    return cgsl;
  }

  public void setCgsl(String cgsl) {
    this.cgsl = cgsl;
  }


  public String getQybh() {
    return qybh;
  }

  public void setQybh(String qybh) {
    this.qybh = qybh;
  }


  public String getSfsy() {
    return sfsy;
  }

  public void setSfsy(String sfsy) {
    this.sfsy = sfsy;
  }


  public String getJmsfzmhm() {
    return jmsfzmhm;
  }

  public void setJmsfzmhm(String jmsfzmhm) {
    this.jmsfzmhm = jmsfzmhm;
  }


  public String getXh() {
    return xh;
  }

  public void setXh(String xh) {
    this.xh = xh;
  }


  public String getFzjg() {
    return fzjg;
  }

  public void setFzjg(String fzjg) {
    this.fzjg = fzjg;
  }


  public String getQyfzjg() {
    return qyfzjg;
  }

  public void setQyfzjg(String qyfzjg) {
    this.qyfzjg = qyfzjg;
  }


  public String getSfyd() {
    return sfyd;
  }

  public void setSfyd(String sfyd) {
    this.sfyd = sfyd;
  }


  public String getQfrq() {
    return qfrq;
  }

  public void setQfrq(String qfrq) {
    this.qfrq = qfrq;
  }


  public String getCyzgzh() {
    return cyzgzh;
  }

  public void setCyzgzh(String cyzgzh) {
    this.cyzgzh = cyzgzh;
  }


  public String getZjcx() {
    return zjcx;
  }

  public void setZjcx(String zjcx) {
    this.zjcx = zjcx;
  }


  public long getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(long updateTime) {
    this.updateTime = updateTime;
  }

}
