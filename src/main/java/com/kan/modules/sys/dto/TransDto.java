package com.kan.modules.sys.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/5/17 16:04
 * @Version: 1.0
 */
@Data
@TableName("t_transpcorp_veh")
public class TransDto {

    @TableId
    public Long ttvId;
    public String cllx;
    public String cllxStr;
    public String qybh;
    public String hphm;
    public String hpzl;
    public String hpzlStr;
    public String xh;
    public String zt;
    public String ztStr;
    public String qzbfqz;
    public String yxqz;
    public String syr;
    public String syxz;
    public String syxzStr;
    public String ysqy;
    public String ysfw;
    public String sfxsjly;
    public String sfgps;
    public String ysfwStr;
    public String sfxsjlyStr;
    public String sfgpsStr;
    public String fdjh;
    public String sjhm;
    public String sfzmhm;
    public String dwmc;
    public String fzjg;
    public String qyfzjg;
    public String dybj;
    public String dybjStr;
    public String updateTime;

}
