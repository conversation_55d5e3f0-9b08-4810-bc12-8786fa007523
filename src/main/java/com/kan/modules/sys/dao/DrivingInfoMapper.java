package com.kan.modules.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kan.modules.sys.dto.CakeResp;
import com.kan.modules.sys.dto.DrivingInfoEntity;
import com.kan.modules.sys.dto.OpenTemp;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2023-08-30 20:58
 * @Version: 1.0
 */
@Mapper
public interface DrivingInfoMapper extends BaseMapper<DrivingInfoEntity> {
    CakeResp getCard();

    CakeResp getDriving();

    CakeResp getPractice();

    OpenTemp getOpenId();

}
