package com.kan.modules.oil.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 油品提取预约响应DTO
 */
@Data
@ApiModel(value = "油品提取预约响应", description = "油品提取预约响应")
public class OilReservationRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预约ID")
    private String id;

    @ApiModelProperty(value = "预定提油时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scheduledTime;

    @ApiModelProperty(value = "油库")
    private String oilDepot;

    @ApiModelProperty(value = "油品种类")
    private String oilType;

    @ApiModelProperty(value = "提油吨数")
    private BigDecimal oilTonnage;

    @ApiModelProperty(value = "车牌号")
    private String licensePlate;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机身份证号")
    private String driverIdCard;

    @ApiModelProperty(value = "司机手机号码")
    private String driverPhone;

    @ApiModelProperty(value = "司机驾驶证号")
    private String driverLicenseNumber;

    @ApiModelProperty(value = "驾驶证档案号")
    private String licenseArchiveNumber;

    @ApiModelProperty(value = "车辆仓数")
    private String tankCount;

    @ApiModelProperty(value = "第一仓装载量（吨）")
    private BigDecimal tank1LoadAmount;

    @ApiModelProperty(value = "第一仓油品类型")
    private String tank1OilType;

    @ApiModelProperty(value = "第二仓装载量（吨）")
    private BigDecimal tank2LoadAmount;

    @ApiModelProperty(value = "第二仓油品类型")
    private String tank2OilType;

    @ApiModelProperty(value = "第三仓装载量（吨）")
    private BigDecimal tank3LoadAmount;

    @ApiModelProperty(value = "第三仓油品类型")
    private String tank3OilType;

    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date submitTime;

    @ApiModelProperty(value = "审批状态（0-待审批，1-审批通过，2-审批拒绝）")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审批状态描述")
    private String approvalStatusDesc;

    @ApiModelProperty(value = "审核状态（0-待审核，1-审核通过，2-审核拒绝）")
    private Integer reviewStatus;

    @ApiModelProperty(value = "审核状态描述")
    private String reviewStatusDesc;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "创建人姓名")
    private String createBy;

    @ApiModelProperty(value = "备注")
    private String remark;



    /**
     * 获取审批状态描述
     */
    public String getApprovalStatusDesc() {
        if (approvalStatus == null) {
            return "未知";
        }
        switch (approvalStatus) {
            case 0:
                return "待审批";
            case 1:
                return "审批通过";
            case 2:
                return "审批拒绝";
            default:
                return "未知";
        }
    }

    /**
     * 获取审核状态描述
     */
    public String getReviewStatusDesc() {
        if (reviewStatus == null) {
            return "未知";
        }
        switch (reviewStatus) {
            case 0:
                return "待审核";
            case 1:
                return "审核通过";
            case 2:
                return "审核拒绝";
            default:
                return "未知";
        }
    }
}
