package com.kan.modules.oil.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 油品提取预约响应DTO
 */
@Data
@ApiModel(value = "油品提取预约响应", description = "油品提取预约响应")
public class OilReservationRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预约ID")
    private String id;

    @ApiModelProperty(value = "预定提油时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date yuanDingTiYaoShiJian;

    @ApiModelProperty(value = "油库")
    private String youKu;

    @ApiModelProperty(value = "油品种类")
    private String youPingLeiBie;

    @ApiModelProperty(value = "提油吨数")
    private BigDecimal tiYouDunShu;

    @ApiModelProperty(value = "车牌号")
    private String chePaiHao;

    @ApiModelProperty(value = "司机姓名")
    private String siJiXingMing;

    @ApiModelProperty(value = "司机身份证号")
    private String siJiShenFenZhengHao;

    @ApiModelProperty(value = "司机手机号码")
    private String siJiShouJiHaoMa;

    @ApiModelProperty(value = "司机驾驶证号")
    private String siJiJiaShiZhengHao;

    @ApiModelProperty(value = "驾驶证档案号")
    private String jiaShiZhengDangAnHao;

    @ApiModelProperty(value = "车辆仓数")
    private String cheLiangCangShu;



    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tiJiaoShiJian;

    @ApiModelProperty(value = "审批状态（0-待审批，1-审批通过，2-审批拒绝）")
    private Integer shenPiZhuangTai;

    @ApiModelProperty(value = "审批状态描述")
    private String shenPiZhuangTaiDesc;

    @ApiModelProperty(value = "审核状态（0-待审核，1-审核通过，2-审核拒绝）")
    private Integer shenHeZhuangTai;

    @ApiModelProperty(value = "审核状态描述")
    private String shenHeZhuangTaiDesc;



    @ApiModelProperty(value = "备注")
    private String remark;

}
