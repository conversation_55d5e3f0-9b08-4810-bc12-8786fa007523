package com.kan.modules.oil.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.kan.common.validator.group.AddGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 油品提取预约新增请求DTO
 */
@Data
@ApiModel(value = "油品提取预约新增请求", description = "油品提取预约新增请求")
public class OilReservationSaveReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预定提油时间", required = true)
    @NotNull(message = "预定提油时间不能为空", groups = {AddGroup.class})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scheduledTime;

    @ApiModelProperty(value = "油库", required = true)
    @NotBlank(message = "油库不能为空", groups = {AddGroup.class})
    @Size(max = 100, message = "油库名称长度不能超过100个字符", groups = {AddGroup.class})
    private String oilDepot;

    @ApiModelProperty(value = "油品种类（0#、92#、95#）", required = true)
    @NotBlank(message = "油品种类不能为空", groups = {AddGroup.class})
    @Pattern(regexp = "^(0#|92#|95#)$", message = "油品种类只能是0#、92#、95#", groups = {AddGroup.class})
    private String oilType;

    @ApiModelProperty(value = "提油吨数", required = true)
    @NotNull(message = "提油吨数不能为空", groups = {AddGroup.class})
    @DecimalMin(value = "0.01", message = "提油吨数必须大于0", groups = {AddGroup.class})
    @DecimalMax(value = "999.99", message = "提油吨数不能超过999.99吨", groups = {AddGroup.class})
    private BigDecimal oilTonnage;

    @ApiModelProperty(value = "车牌号", required = true)
    @NotBlank(message = "车牌号不能为空", groups = {AddGroup.class})
    @Pattern(regexp = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$", 
             message = "车牌号格式不正确", groups = {AddGroup.class})
    private String licensePlate;

    @ApiModelProperty(value = "司机姓名", required = true)
    @NotBlank(message = "司机姓名不能为空", groups = {AddGroup.class})
    @Size(max = 50, message = "司机姓名长度不能超过50个字符", groups = {AddGroup.class})
    private String driverName;

    @ApiModelProperty(value = "司机身份证号", required = true)
    @NotBlank(message = "司机身份证号不能为空", groups = {AddGroup.class})
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确", groups = {AddGroup.class})
    private String driverIdCard;

    @ApiModelProperty(value = "司机手机号码", required = true)
    @NotBlank(message = "司机手机号码不能为空", groups = {AddGroup.class})
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确", groups = {AddGroup.class})
    private String driverPhone;

    @ApiModelProperty(value = "司机驾驶证号", required = true)
    @NotBlank(message = "司机驾驶证号不能为空", groups = {AddGroup.class})
    @Size(max = 50, message = "驾驶证号长度不能超过50个字符", groups = {AddGroup.class})
    private String driverLicenseNumber;

    @ApiModelProperty(value = "驾驶证档案号", required = true)
    @NotBlank(message = "驾驶证档案号不能为空", groups = {AddGroup.class})
    @Size(max = 50, message = "驾驶证档案号长度不能超过50个字符", groups = {AddGroup.class})
    private String licenseArchiveNumber;

    @ApiModelProperty(value = "车辆仓数（单仓/双仓/三仓）", required = true)
    @NotBlank(message = "车辆仓数不能为空", groups = {AddGroup.class})
    @Pattern(regexp = "^(单仓|双仓|三仓)$", message = "车辆仓数只能是单仓、双仓、三仓", groups = {AddGroup.class})
    private String tankCount;

    @ApiModelProperty(value = "各仓装载信息", required = true)
    @NotEmpty(message = "各仓装载信息不能为空", groups = {AddGroup.class})
    @Valid
    private List<TankLoadInfoDTO> tankLoadInfo;

    @ApiModelProperty(value = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符", groups = {AddGroup.class})
    private String remark;

    /**
     * 各仓装载信息DTO
     */
    @Data
    @ApiModel(value = "各仓装载信息", description = "各仓装载信息")
    public static class TankLoadInfoDTO implements Serializable {
        
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "仓号（第几仓）", required = true)
        @NotNull(message = "仓号不能为空", groups = {AddGroup.class})
        @Min(value = 1, message = "仓号必须大于0", groups = {AddGroup.class})
        @Max(value = 3, message = "仓号不能超过3", groups = {AddGroup.class})
        private Integer tankNumber;

        @ApiModelProperty(value = "装载量（吨）", required = true)
        @NotNull(message = "装载量不能为空", groups = {AddGroup.class})
        @DecimalMin(value = "0.01", message = "装载量必须大于0", groups = {AddGroup.class})
        @DecimalMax(value = "999.99", message = "装载量不能超过999.99吨", groups = {AddGroup.class})
        private BigDecimal loadAmount;

        @ApiModelProperty(value = "油品类型", required = true)
        @NotBlank(message = "油品类型不能为空", groups = {AddGroup.class})
        @Pattern(regexp = "^(0#|92#|95#)$", message = "油品类型只能是0#、92#、95#", groups = {AddGroup.class})
        private String oilType;
    }
}
