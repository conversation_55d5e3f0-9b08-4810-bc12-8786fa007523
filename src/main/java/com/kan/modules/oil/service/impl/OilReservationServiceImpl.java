package com.kan.modules.oil.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kan.common.exception.KanException;
import com.kan.modules.oil.dto.request.OilReservationSaveReqDTO;
import com.kan.modules.oil.dto.response.OilReservationRespDTO;
import com.kan.modules.oil.entity.OilReservation;
import com.kan.modules.oil.service.OilReservationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 油品提取预约服务实现类
 */
@Slf4j
@Service
public class OilReservationServiceImpl implements OilReservationService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public String save(OilReservationSaveReqDTO reqDTO) {
        try {
            // 生成预约ID
            String reservationId = IdWorker.getIdStr();
            
            // 创建实体对象
            OilReservation reservation = new OilReservation();
            
            // 复制基本属性
            BeanUtils.copyProperties(reqDTO, reservation);
            reservation.setId(reservationId);
            
            // 设置时间信息
            Date now = new Date();
            reservation.setSubmitTime(now);
            reservation.setCreateTime(now);
            reservation.setUpdateTime(now);
            
            // 设置审批状态为待审批
            reservation.setApprovalStatus(0);
            
            // 设置删除标志
            reservation.setDelFlag(false);
            
            // TODO: 从当前登录用户获取创建人信息
            // 这里暂时设置为系统用户，实际应该从SecurityContext或Session中获取
            reservation.setCreateById("system");
            reservation.setCreateBy("系统用户");
            reservation.setUpdateById("system");
            reservation.setUpdateBy("系统用户");
            
            // 转换各仓装载信息
            if (!CollectionUtils.isEmpty(reqDTO.getTankLoadInfo())) {
                List<OilReservation.TankLoadInfo> tankLoadInfoList = reqDTO.getTankLoadInfo().stream()
                    .map(dto -> {
                        OilReservation.TankLoadInfo tankLoadInfo = new OilReservation.TankLoadInfo();
                        BeanUtils.copyProperties(dto, tankLoadInfo);
                        return tankLoadInfo;
                    })
                    .collect(Collectors.toList());
                reservation.setTankLoadInfo(tankLoadInfoList);
            }
            
            // 数据验证
            validateReservation(reservation);
            
            // 保存到MongoDB
            mongoTemplate.save(reservation);
            
            log.info("油品提取预约保存成功，预约ID: {}", reservationId);
            return reservationId;
            
        } catch (Exception e) {
            log.error("保存油品提取预约失败", e);
            throw new KanException("保存油品提取预约失败: " + e.getMessage());
        }
    }

    @Override
    public OilReservationRespDTO findById(String id) {
        try {
            OilReservation reservation = getById(id);
            if (reservation == null) {
                return null;
            }
            
            // 转换为响应DTO
            OilReservationRespDTO respDTO = new OilReservationRespDTO();
            BeanUtils.copyProperties(reservation, respDTO);
            
            // 转换各仓装载信息
            if (!CollectionUtils.isEmpty(reservation.getTankLoadInfo())) {
                List<OilReservationRespDTO.TankLoadInfoRespDTO> tankLoadInfoList = 
                    reservation.getTankLoadInfo().stream()
                        .map(entity -> {
                            OilReservationRespDTO.TankLoadInfoRespDTO dto = 
                                new OilReservationRespDTO.TankLoadInfoRespDTO();
                            BeanUtils.copyProperties(entity, dto);
                            return dto;
                        })
                        .collect(Collectors.toList());
                respDTO.setTankLoadInfo(tankLoadInfoList);
            }
            
            return respDTO;
            
        } catch (Exception e) {
            log.error("查询油品提取预约失败，ID: {}", id, e);
            throw new KanException("查询油品提取预约失败: " + e.getMessage());
        }
    }

    @Override
    public OilReservation getById(String id) {
        try {
            Query query = new Query(Criteria.where("id").is(id).and("delFlag").ne(true));
            return mongoTemplate.findOne(query, OilReservation.class);
        } catch (Exception e) {
            log.error("根据ID查询油品提取预约失败，ID: {}", id, e);
            throw new KanException("查询油品提取预约失败: " + e.getMessage());
        }
    }

    /**
     * 验证预约数据
     */
    private void validateReservation(OilReservation reservation) {
        // 验证预定时间不能是过去时间
        if (reservation.getScheduledTime().before(new Date())) {
            throw new KanException("预定提油时间不能是过去时间");
        }
        
        // 验证各仓装载量总和是否等于提油吨数
        if (!CollectionUtils.isEmpty(reservation.getTankLoadInfo())) {
            BigDecimal totalLoadAmount = reservation.getTankLoadInfo().stream()
                .map(OilReservation.TankLoadInfo::getLoadAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            if (totalLoadAmount.compareTo(reservation.getOilTonnage()) != 0) {
                throw new KanException("各仓装载量总和必须等于提油吨数");
            }
        }
        
        // 验证仓数与装载信息是否匹配
        int expectedTankCount = getTankCountNumber(reservation.getTankCount());
        if (reservation.getTankLoadInfo().size() != expectedTankCount) {
            throw new KanException("各仓装载信息数量与车辆仓数不匹配");
        }
    }

    /**
     * 获取仓数数字
     */
    private int getTankCountNumber(String tankCount) {
        switch (tankCount) {
            case "单仓":
                return 1;
            case "双仓":
                return 2;
            case "三仓":
                return 3;
            default:
                throw new KanException("不支持的车辆仓数: " + tankCount);
        }
    }
}
