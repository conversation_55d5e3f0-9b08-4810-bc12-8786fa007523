package com.kan.modules.oil.service.impl;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.kan.common.exception.KanException;
import com.kan.modules.oil.dto.request.OilReservationSaveReqDTO;
import com.kan.modules.oil.dto.response.OilReservationRespDTO;
import com.kan.modules.oil.entity.OilReservation;
import com.kan.modules.oil.service.OilReservationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 油品提取预约服务实现类
 */
@Slf4j
@Service
public class OilReservationServiceImpl implements OilReservationService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public String save(OilReservationSaveReqDTO reqDTO) {
        try {
            // 生成预约ID
            String reservationId = IdWorker.getIdStr();
            // 创建实体对象
            OilReservation reservation = new OilReservation();
            // 复制基本属性
            BeanUtils.copyProperties(reqDTO, reservation);
            reservation.setId(reservationId);
            // 设置时间信息
            Date now = new Date();
            reservation.setTiJiaoShiJian(now);
            // 设置审批状态为待审批，审核状态为待审核
            reservation.setShenPiZhuangTai(0);
            reservation.setShenHeZhuangTai(0);
            // 设置删除标志
            reservation.setDelFlag(false);
            // 保存到MongoDB
            mongoTemplate.save(reservation);
            
            log.info("油品提取预约保存成功，预约ID: {}", reservationId);
            return reservationId;
            
        } catch (Exception e) {
            log.error("保存油品提取预约失败", e);
            throw new KanException("保存油品提取预约失败: " + e.getMessage());
        }
    }

    @Override
    public OilReservationRespDTO findById(String id) {
        try {
            OilReservation reservation = getById(id);
            if (reservation == null) {
                return null;
            }
            
            // 转换为响应DTO
            OilReservationRespDTO respDTO = new OilReservationRespDTO();
            BeanUtils.copyProperties(reservation, respDTO);
            
            return respDTO;
            
        } catch (Exception e) {
            log.error("查询油品提取预约失败，ID: {}", id, e);
            throw new KanException("查询油品提取预约失败: " + e.getMessage());
        }
    }

    @Override
    public OilReservation getById(String id) {
        try {
            Query query = new Query(Criteria.where("id").is(id).and("delFlag").ne(true));
            return mongoTemplate.findOne(query, OilReservation.class);
        } catch (Exception e) {
            log.error("根据ID查询油品提取预约失败，ID: {}", id, e);
            throw new KanException("查询油品提取预约失败: " + e.getMessage());
        }
    }
}
