package com.kan.modules.oil.service;

import com.kan.modules.oil.dto.request.OilReservationSaveReqDTO;
import com.kan.modules.oil.dto.response.OilReservationRespDTO;
import com.kan.modules.oil.entity.OilReservation;

import java.util.List;

/**
 * 油品提取预约服务接口
 */
public interface OilReservationService {

    /**
     * 新增油品提取预约
     * @param reqDTO 请求参数
     * @return 预约ID
     */
    String save(OilReservationSaveReqDTO reqDTO);

    /**
     * 根据ID查询油品提取预约详情
     * @param id 预约ID
     * @return 预约详情
     */
    OilReservationRespDTO findById(String id);

    /**
     * 根据ID查询油品提取预约实体
     * @param id 预约ID
     * @return 预约实体
     */
    OilReservation getById(String id);

    List<OilReservationRespDTO> selectList();


}
