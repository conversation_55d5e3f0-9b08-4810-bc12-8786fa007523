package com.kan.modules.oil.entity;

import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 油品提取预约实体类
 * 存储到MongoDB中
 */
@Data
@Document(collection = "oil_reservation")
public class OilReservation implements Serializable {

    private static final long serialVersionUID = 1L;

    @MongoId
    private ObjectId _id;

    @Field("id")
    private String id;

    /**
     * 预定提油时间
     */
    @Field("scheduledTime")
    private Date scheduledTime;

    /**
     * 油库
     */
    @Field("oilDepot")
    private String oilDepot;

    /**
     * 油品种类（0#、92#、95#）
     */
    @Field("oilType")
    private String oilType;

    /**
     * 提油吨数
     */
    @Field("oilTonnage")
    private BigDecimal oilTonnage;

    /**
     * 车牌号
     */
    @Field("licensePlate")
    private String licensePlate;

    /**
     * 司机姓名
     */
    @Field("driverName")
    private String driverName;

    /**
     * 司机身份证号
     */
    @Field("driverIdCard")
    private String driverIdCard;

    /**
     * 司机手机号码
     */
    @Field("driverPhone")
    private String driverPhone;

    /**
     * 司机驾驶证号
     */
    @Field("driverLicenseNumber")
    private String driverLicenseNumber;

    /**
     * 驾驶证档案号
     */
    @Field("licenseArchiveNumber")
    private String licenseArchiveNumber;

    /**
     * 车辆仓数（单仓/双仓/三仓）
     */
    @Field("tankCount")
    private String tankCount;

    /**
     * 第三仓油品类型
     */
    @Field("tankOilType")
    private String tankOilType;

    /**
     * 提交时间
     */
    @Field("submitTime")
    private Date submitTime;

    /**
     * 审批状态（0-待审批，1-审批通过，2-审批拒绝）
     */
    @Field("approvalStatus")
    private Integer approvalStatus;

    /**
     * 审核状态（0-待审核，1-审核通过，2-审核拒绝）
     */
    @Field("reviewStatus")
    private Integer reviewStatus;

    /**
     * 创建时间
     */
    @Field("createTime")
    private Date createTime;

    /**
     * 创建人ID
     */
    @Field("createById")
    private String createById;

    /**
     * 创建人姓名
     */
    @Field("createBy")
    private String createBy;

    /**
     * 更新时间
     */
    @Field("updateTime")
    private Date updateTime;

    /**
     * 更新人ID
     */
    @Field("updateById")
    private String updateById;

    /**
     * 更新人姓名
     */
    @Field("updateBy")
    private String updateBy;

    /**
     * 删除标志
     */
    @Field("delFlag")
    private Boolean delFlag;

    /**
     * 备注
     */
    @Field("remark")
    private String remark;


}
