package com.kan.modules.oil.entity;

import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 油品提取预约实体类
 * 存储到MongoDB中
 */
@Data
@Document(collection = "oil_reservation")
public class OilReservation implements Serializable {

    private static final long serialVersionUID = 1L;

    @MongoId
    private ObjectId _id;

    @Field("id")
    private String id;

    /**
     * 预定提油时间
     */
    @Field("yuanDingTiYaoShiJian")
    private Date yuanDingTiYaoShiJian;

    /**
     * 油库
     */
    @Field("youKu")
    private String youKu;

    /**
     * 油品种类（0#、92#、95#）
     */
    @Field("youPingLeiBie")
    private String youPingLeiBie;

    /**
     * 提油吨数
     */
    @Field("tiYouDunShu")
    private BigDecimal tiYouDunShu;

    /**
     * 车牌号
     */
    @Field("chePaiHao")
    private String chePaiHao;

    /**
     * 司机姓名
     */
    @Field("siJiXingMing")
    private String siJiXingMing;

    /**
     * 司机身份证号
     */
    @Field("siJiShenFenZhengHao")
    private String siJiShenFenZhengHao;

    /**
     * 司机手机号码
     */
    @Field("siJiShouJiHaoMa")
    private String siJiShouJiHaoMa;

    /**
     * 司机驾驶证号
     */
    @Field("siJiJiaShiZhengHao")
    private String siJiJiaShiZhengHao;

    /**
     * 驾驶证档案号
     */
    @Field("jiaShiZhengDangAnHao")
    private String jiaShiZhengDangAnHao;

    /**
     * 车辆仓数（单仓/双仓/三仓）
     */
    @Field("cheLiangCangShu")
    private String cheLiangCangShu;

    /**
     * 第三仓油品类型
     */
    @Field("youPinLeiBie")
    private String youPinLeiBie;

    /**
     * 提交时间
     */
    @Field("tiJiaoShiJian")
    private Date tiJiaoShiJian;

    /**
     * 审批状态（0-待审批，1-审批通过，2-审批拒绝）
     */
    @Field("shenPiZhuangTai")
    private Integer shenPiZhuangTai;

    /**
     * 审核状态（0-待审核，1-审核通过，2-审核拒绝）
     */
    @Field("shenHeZhuangTai")
    private Integer shenHeZhuangTai;

    @Field("chuangJianRen")
    private String chuangJianRen;
    /**
     * 删除标志
     */
    @Field("delFlag")
    private Boolean delFlag;
}
