package com.kan.modules.oil.controller;

import com.kan.common.validator.ValidatorUtils;
import com.kan.common.validator.group.AddGroup;
import com.kan.core.api.ApiRest;
import com.kan.core.api.controller.BaseController;
import com.kan.core.api.dto.BaseIdRespDTO;
import com.kan.modules.oil.dto.request.OilReservationSaveReqDTO;
import com.kan.modules.oil.dto.response.OilReservationRespDTO;
import com.kan.modules.oil.service.OilReservationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 油品提取预约控制器
 */
@Slf4j
@Api(tags = {"油品提取预约"})
@RestController
@RequestMapping("/exam/api/oil/reservation")
public class OilReservationController extends BaseController {

    @Autowired
    private OilReservationService oilReservationService;

    /**
     * 新增油品提取预约
     * @param reqDTO 请求参数
     * @return 预约ID
     */
    @ApiOperation(value = "新增油品提取预约", notes = "创建新的油品提取预约申请")
    @PostMapping("/save")
    @CrossOrigin
    public ApiRest<BaseIdRespDTO> save(@RequestBody OilReservationSaveReqDTO reqDTO) {
        try {
            // 数据验证
            ValidatorUtils.validateEntity(reqDTO, AddGroup.class);
            
            // 保存预约
            String reservationId = oilReservationService.save(reqDTO);
            
            log.info("油品提取预约创建成功，预约ID: {}", reservationId);
            return super.success(new BaseIdRespDTO(reservationId));
            
        } catch (Exception e) {
            log.error("新增油品提取预约失败", e);
            return super.failure("新增油品提取预约失败: " + e.getMessage());
        }
    }

    /**
     * 查询油品提取预约详情
     * @param id 预约ID
     * @return 预约详情
     */
    @ApiOperation(value = "查询油品提取预约详情", notes = "根据预约ID查询预约详情")
    @GetMapping("/detail/{id}")
    @CrossOrigin
    public ApiRest<OilReservationRespDTO> detail(@PathVariable("id") String id) {
        try {
            OilReservationRespDTO respDTO = oilReservationService.findById(id);
            if (respDTO == null) {
                return super.failure("预约信息不存在");
            }
            
            return super.success(respDTO);
            
        } catch (Exception e) {
            log.error("查询油品提取预约详情失败，ID: {}", id, e);
            return super.failure("查询预约详情失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口连通性
     * @return 测试结果
     */
    @ApiOperation(value = "测试接口", notes = "测试油品提取预约接口是否正常")
    @GetMapping("/test")
    @CrossOrigin
    public ApiRest<String> test() {
        return super.success("油品提取预约接口正常运行");
    }
}
