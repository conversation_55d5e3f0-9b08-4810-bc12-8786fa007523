package com.kan.modules.oss.service.impl;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.github.tobato.fastdfs.domain.conn.FdfsWebServer;
import com.github.tobato.fastdfs.domain.fdfs.MetaData;
import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import com.kan.modules.oss.service.FdfsClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class FdfsClientServiceImpl implements FdfsClientService {
    @Value("${oss.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.roleArn}")
    private String roleArn;

    @Value("${oss.region}")
    private String region;

    @Value("${oss.bucket}")
    private String bucket;
    @Value("${oss.endpoint}")
    private String endpoint;
    @Value("${fdfs.webServerUrl}")
    private String baseFilePath;
    @Autowired
    private FdfsWebServer fdfsWebServer;
    @Resource
    FastFileStorageClient fastFileStorageClient;

    @Override
    public String uploadFile(MultipartFile file) throws IOException {
        if (file != null) {
            byte[] bytes = file.getBytes();
            long fileSize = file.getSize();
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            return this.uploadFile(bytes, fileSize, originalFilename,extension);
        }
        return null;
    }
    @Override
    public Map<String, Object> uploadFileMap(MultipartFile file) throws IOException {
        if (file != null) {
            byte[] bytes = file.getBytes();
            long fileSize = file.getSize();
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            return this.uploadFileMap(bytes, fileSize, originalFilename,extension);
        }
        return null;
    }
    @Override
    public Map<String, Object> uploadFileMap(byte[] bytes, long fileSize, String fileName, String extension) {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        Set<MetaData> metaDataSet = new HashSet<>();
        metaDataSet.add(new MetaData("dateTime", LocalDateTime.now().toString()));
        StorePath storePath = fastFileStorageClient.uploadFile(byteArrayInputStream, fileSize, extension, metaDataSet);
        Map<String, Object> fileMap = new HashMap<String, Object>() {{
            put("fileUrl", fdfsWebServer.getWebServerUrl() + storePath.getFullPath());
        }};

        return fileMap;
    }
    @Override
    public String uploadFile(byte[] bytes, long fileSize, String fileName, String extension) {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        Set<MetaData> metaDataSet = new HashSet<>();
        metaDataSet.add(new MetaData("dateTime", LocalDateTime.now().toString()));
        StorePath storePath = fastFileStorageClient.uploadFile(byteArrayInputStream, fileSize, extension, metaDataSet);
        return baseFilePath + storePath.getFullPath();
    }

    @Override
    public Map<String, Object> ossUploadFile(MultipartFile file) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            // 填写Byte数组。
            byte[] content = file.getBytes();
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, extension,
                    new ByteArrayInputStream(content));
            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putObjectRequest.setProcess("true");

            // 创建PutObject请求。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            // 如果上传成功，则返回200。
//            System.out.println(result.getResponse().getStatusCode());
            String eTag = result.getETag();
            return new HashMap<String, Object>() {{
                put("fileUrl", eTag);
            }};
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        }catch (IOException e){
            e.printStackTrace();
        }finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }
}
