package com.kan.modules.oss.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

public interface FdfsClientService {


    /**
     * @param file
     * @return 返回文件路径以及文件访问url
     * @throws IOException
     */
    String uploadFile(MultipartFile file) throws IOException;
    /**
     * @param file
     * @return 返回文件路径以及文件访问url
     * @throws IOException
     */
    Map<String, Object> uploadFileMap(MultipartFile file) throws IOException;

    /**
     * 文件上传
     *
     * @param bytes     文件字节
     * @param fileSize  文件大小
     * @param fileName  文件名称
     * @param extension 文件扩展名
     * @return 返回文件路径以及文件访问url
     */
    String uploadFile(byte[] bytes, long fileSize, String fileName, String extension);

    /**
     * 文件上传
     *
     * @param bytes     文件字节
     * @param fileSize  文件大小
     * @param fileName  文件名称
     * @param extension 文件扩展名
     * @return 返回文件路径以及文件访问url
     */
    Map<String, Object> uploadFileMap(byte[] bytes, long fileSize, String fileName, String extension);

    /**
     * Oss文件上传
     * @param file
     * @return
     */
    Map<String, Object> ossUploadFile(MultipartFile file);
}
