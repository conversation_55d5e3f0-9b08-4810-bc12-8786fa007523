package com.kan.modules.oss.controller;

import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.kan.common.exception.KanException;
import com.kan.common.utils.Result;
import com.kan.modules.oss.entity.OssVo;
import com.kan.modules.oss.service.FdfsClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequiredArgsConstructor
@RequestMapping("sys/fileManage")
@Slf4j
public class FileManageController {

    @Value("${oss.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.roleArn}")
    private String roleArn;

    @Value("${oss.region}")
    private String region;

    @Value("${oss.bucket}")
    private String bucket;

    private final FdfsClientService fdfsClientService;

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件路径
     */
    @PostMapping(value = "upload")
    @ResponseBody
    public Result uploadFile(MultipartFile file) throws IOException {
        return new Result().data(fdfsClientService.uploadFile(file));
    }


    /**
     * 获取阿里OSS文件上传SecurityToken
     *
     */
    @GetMapping(value = "getToken")
    @ResponseBody
    public Result getToken() {
        String roleSessionName = "session-name";// 自定义即可
        // 定制你的policy
        String policy ="{\n" +
                "    \"Statement\": [\n" +
                "        {\n" +
                "            \"Action\": \"oss:*\",\n" +
                "            \"Effect\": \"Allow\",\n" +
                "            \"Resource\": \"*\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"Version\": \"1\"\n" +
                "}";
        OssVo ossVo=new OssVo();

        ossVo.setBucket(bucket);
        ossVo.setRegion(region);
        try {
            AssumeRoleResponse response = OssVo.assumeRole(accessKeyId, accessKeySecret, roleArn, roleSessionName,
                    policy);
            ossVo.setSecurityToken(response.getCredentials().getSecurityToken());
            ossVo.setAccessKeyId(response.getCredentials().getAccessKeyId());
            ossVo.setAccessKeySecret(response.getCredentials().getAccessKeySecret());
        } catch (ClientException e) {
            throw new KanException("Error message: " + e.getErrMsg());
        }

        return  new Result().data(ossVo);
    }
}
