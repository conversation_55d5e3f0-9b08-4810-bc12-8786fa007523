package com.kan.modules.hfive.service;

import com.kan.modules.hfive.dto.DriverInformation;
import com.kan.modules.hfive.dto.resp.DeiverUserAgreeResp;
import com.kan.modules.hfive.mongoDto.deiver_user_agree;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023-12-05 16:02
 * @Version: 1.0
 */
public interface DriverService {

    Boolean add(DriverInformation driverInformation);

    DriverInformation getOne(DriverInformation driverInformation);

    List<DeiverUserAgreeResp> getDeiverAgree();

}
