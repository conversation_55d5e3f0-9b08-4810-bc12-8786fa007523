package com.kan.modules.hfive.controller;

import com.kan.common.utils.Result;
import com.kan.modules.hfive.dto.DriverInformation;
import com.kan.modules.hfive.service.DriverService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-05 15:59
 * @Version: 1.0
 */
@RestController
@RequestMapping("dri")
public class DriverController {
    @Resource
    DriverService driverService;
    @PostMapping("add")
    @ApiOperation(value = "add" , notes = "新增企业基本信息")
    public Result add(@RequestBody DriverInformation driverInformation){
        Boolean status = driverService.add(driverInformation);
        if (!status){
            return Result.addError();
        }
        return new Result();
    }
    @PostMapping("getOne")
    public Result getList(@RequestBody DriverInformation driverInformation){

        return new Result().data(driverService.getOne(driverInformation));
    }

    @PostMapping("getDeiverAgree")
    public Result getDeiverAgree(){

        return new Result().data(driverService.getDeiverAgree());
    }
}
