package com.kan.modules.datav.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static final String FORMAT_MONTH_DAY_CN = "MM月dd日";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String FORMAT_DATE_WITHOUT_LINK = "yyyyMMdd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    public static Date getDayByIndex(int day){
        Date now = new Date();
        LocalDate localDate=now.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate yesterday = localDate.plusDays(day);
        Date newDate=java.sql.Date.valueOf(yesterday);
        return newDate;
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 当月有多少天
     * @param date
     * @return int
     */
    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 日期是当月的第几天
     * @param date
     * @return int
     */
    public static int getDayInMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取月份
     * @param
     * @return int
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    /**
     * Description: 按指定格式格式化日期
     *
     * @param date  * @param format
     * @return:
     */
    public static String formatDate(LocalDate date, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return date.format(formatter);
    }

    public static LocalDate parseDate(String date, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return LocalDate.parse(date, formatter);
    }

    public static List<String> getDates(String startDate, String endDate, String format) {
        List<String> list = Lists.newArrayList();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(format);
        LocalDate tmp = LocalDate.parse(startDate, dateFormatter);
        LocalDate end = LocalDate.parse(endDate, dateFormatter);
        while (tmp.isBefore(end)) {
            list.add(formatDate(tmp, format));
            tmp = tmp.plusDays(1);
        }
        list.add(formatDate(end, format));
        return list;
    }

    public static List<Pair<Date,Date>> get24HourRegine(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        List<Pair<Date,Date>>  list = Lists.newArrayList();
        Date pre = calendar.getTime();
        for(int i=1;i<=96;i++){
            calendar.add(Calendar.MINUTE,i*15);
            Date now = new Date(calendar.getTime().getTime());
            Pair<Date,Date> pair = Pair.of(new Date(pre.getTime()),now);
            list.add(pair);
            pre = now;
        }
        return list;
    }

    public static List<String> get24HourList(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        List<String>  list = Lists.newArrayList();
        SimpleDateFormat df = new SimpleDateFormat("HH:mm");
        for(int i=1;i<=96;i++){
            calendar.add(Calendar.MINUTE,15);
            Date now = new Date(calendar.getTime().getTime());
            list.add(df.format(now));
        }
        return list;
    }

    /**
     * 计算日期之间的天数
     * @throws ParseException
     */
    public static Long getDays(String startDate, String endDate){
        Scanner scanner = new Scanner(System.in);
        try {
            LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate begin = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return end.toEpochDay() - begin.toEpochDay();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    /**获取制定月份最后一天**/
    public static String getLastDay(){
        Calendar cal= Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
    }
    public static String getLastMonth(){
        // 创建一个Calendar实例
        Calendar calendar = Calendar.getInstance();

        // 获取当前时间
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);

        // 设置Calendar为上一个月
        calendar.set(Calendar.MONTH, month - 1);

        // 获取上一个月的时间
        int previousYear = calendar.get(Calendar.YEAR);
        int previousMonth = calendar.get(Calendar.MONTH);

        String s = previousYear + "-" + (previousMonth + 1);
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM");
        Date parse = null;
        try {
            parse = format.parse(s);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return format.format(parse);
    }

    public static void main(String[] args) {
        System.out.println(getLastMonth());
    }
    public static String getLastMonthByMonth(String month){
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM");
        Date parse = null;
        try {
            parse = sdf1.parse(month);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(parse);
        rightNow.add(Calendar.MONTH, +1);
        Date dt1 = rightNow.getTime();
        String reStr = sdf1.format(dt1);
        return reStr;
    }
    /**获取当前季度最后一天*/
    public static String getQuarterLastDay(){
        String month = new SimpleDateFormat("M").format(new Date());
        String year = new SimpleDateFormat("yyyy").format(new Date());
        if(Integer.parseInt(month) <= 3){
            return year+"-03-31";
        }
        if(Integer.parseInt(month) <= 6){
            return year+"-06-30";
        }
        if(Integer.parseInt(month) <= 9){
            return year+"-09-30";
        }
        if(Integer.parseInt(month) <= 12){
            return year+"-12-31";
        }
        return null;
    }

    /**获取当前季度*/
    public static String getQuarter(){
        String month = new SimpleDateFormat("M").format(new Date());
        String year = new SimpleDateFormat("yyyy").format(new Date());
        if(Integer.parseInt(month) <= 3){
            return year+"-01";
        }
        if(Integer.parseInt(month) <= 6){
            return year+"-02";
        }
        if(Integer.parseInt(month) <= 9){
            return year+"-03";
        }
        if(Integer.parseInt(month) <= 12){
            return year+"-04";
        }
        return null;
    }

    /**获取当前季度*/
    public static Integer getQuarterInt(){
        String month = new SimpleDateFormat("M").format(new Date());
        String year = new SimpleDateFormat("yyyy").format(new Date());
        if(Integer.parseInt(month) <= 3){
            return 1;
        }
        if(Integer.parseInt(month) <= 6){
            return 2;
        }
        if(Integer.parseInt(month) <= 9){
            return 3;
        }
        if(Integer.parseInt(month) <= 12){
            return 4;
        }
        return null;
    }


    /**
     * 获取本周星期一
     * @return
     */
    public static String getWeekDateStart(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        // 一周第一天为周日，所以此处日+1
        calendar.setWeekDate(calendar.getWeekYear(), calendar.get(Calendar.WEEK_OF_YEAR), 2);
        calendar.set(calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH),
                0, 0, 0);
        return sdf.format(calendar.getTime());
    }

    /**
     * 获取指定年之前的日期
     * @param year
     * @return
     */
    public static Date getBeforeYear(int year) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -year);
        return calendar.getTime();
    }

    /**
     * 获取时间段内所有的日期
     * @param start
     * @param end
     * @return
     * @throws Exception
     */
    public static List<String> getDateList(String start,String end) throws Exception{
        List<String> dates = new ArrayList();
        //创建时间解析对象规定解析格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //将传入的时间解析成Date类型,相当于格式化
        Date dBegin = sdf.parse(start);
        Date dEnd = sdf.parse(end);
        //将格式化后的第一天添加进集合
        dates.add(sdf.format(dBegin));
        //使用本地的时区和区域获取日历
        Calendar calBegin = Calendar.getInstance();
        //传入起始时间将此日历设置为起始日历
        calBegin.setTime(dBegin);
        //判断结束日期前一天是否在起始日历的日期之后
        while (dEnd.after(calBegin.getTime())) {
            //根据日历的规则:月份中的每一天，为起始日历加一天
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            //得到的每一天就添加进集合
            dates.add(sdf.format(calBegin.getTime()));
            //如果当前的起始日历超过结束日期后,就结束循环
        }
        return dates;
    }
    public static List<String> getMonthList(Date startDate, Date endDate) {
        ArrayList<String> result = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");//格式化，调整为自己需要的格式
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        //给calendar设置开始时间
        min.setTime(startDate);
        //set方法设置年月日 年为开始时间的年份 后面同理
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
        //给calendar设置结束时间
        max.setTime(endDate);
        //set方法设置年月日 年为结束时间的年份 后面同理，最后面的1和2不要改
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
        //创建一个临时的变量，代表当前的时间
        Calendar curr = min;
        //如果当前的时间在结束时间之前，循环知道超过结束时间就结束，返回结果集合
        while (curr.before(max)) {
            //将这个当前的时间格式化之后保存到result集合
            result.add(sdf.format(curr.getTime()));
            //将当前的时间加上1个月
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }
    /**
     * 获取指定日期前多少天的日期
     * @param
     * @return int
     */
    public static Date getDateBeforeDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_WEEK,-days);
        return calendar.getTime();
    }

    /**
     * 获取指定日期前多少年的日期
     * @param
     * @return int
     */
    public static Date getDateBeforeYears(Date date, int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR,-year);
        return calendar.getTime();
    }

    /**获取本月月份第一天**/
    public static Date getMonthFirstDate(){
        String date = new SimpleDateFormat("yyyy-MM").format(new Date())+"-01";
        try {
            return new SimpleDateFormat("yyyy-MM-dd").parse(date);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
    /**获取传入月份第一天**/
    public static Date getMonthFirstDate(String dateTime){
        String date = dateTime+"-01";
        try {
            return new SimpleDateFormat("yyyy-MM-dd").parse(date);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
    /**获取传入月份第一天**/
    public static Date getFirstYearDate(String year){
        String date = year+"-01-01";
        try {
            return new SimpleDateFormat("yyyy-MM-dd").parse(date);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
    /**
     * 获取当前月份所在季度后一季度第二月底时间
     * @param month
     * @return
     */
    public static String getMonth(String month) {
        String year = new SimpleDateFormat("yyyy").format(new Date());
        if ("01".equals(month)) {
            return (year + "-05-31");
        }
        if ("02".equals(month)) {
            return (year + "-05-31");
        }
        if ("03".equals(month)) {
            return (year + "-05-31");
        }
        if ("04".equals(month)) {
            return (year + "-08-31");
        }
        if ("05".equals(month)) {
            return (year + "-08-31");
        }
        if ("06".equals(month)) {
            return (year + "-08-31");
        }
        if ("07".equals(month)) {
            return (year + "-11-30");
        }
        if ("08".equals(month)) {
            return (year + "-11-30");
        }
        if ("09".equals(month)) {
            return (year + "-11-30");
        }
        if ("10".equals(month)) {
            return ((Integer.parseInt(year) + 1) + "-02-28");
        }
        if ("11".equals(month)) {
            return ((Integer.parseInt(year) + 1) + "-02-28");
        }
        if ("12".equals(month)) {
            return ((Integer.parseInt(year) + 1) + "-02-28");
        }
        return null;
    }
    /**获取传入月份第一天**/
    public static Date getMonthLastDay(String dataTime){
        SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = dfs.parse(dataTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DATE, 0);
        try {
            return (df.parse(df.format(calendar.getTime())));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**获取制定月份下一月最后一天**/
    public static String getLastMonthLastDay(){
        Date date=new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 2);
        calendar.set(Calendar.DATE, 0);
        return (df.format(calendar.getTime()));
    }
    public static Date getLastMonthFirstDay(){
        Date date=new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        try {
            return (df.parse(df.format(calendar.getTime())));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getMonthLast(String month){
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = format.parse(month);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(parse);
        int lastDay = calendar.getActualMaximum(Calendar.DATE);
        calendar.set(Calendar.DAY_OF_MONTH, lastDay);
        return df.format(calendar.getTime());
    }
}
