package com.kan.modules.datav.utils;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kan.modules.datav.dto.CarLineEntity;
import com.kan.modules.datav.dto.CarTypeNumDto;
import com.kan.modules.datav.job.CarTypeNumEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CarTypeNumMapper extends BaseMapper<CarTypeNumEntity> {
    List<CarTypeNumDto> getCarTypeNum(String deptCode);
}
