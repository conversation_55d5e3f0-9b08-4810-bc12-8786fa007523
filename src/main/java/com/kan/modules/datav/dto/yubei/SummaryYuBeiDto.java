package com.kan.modules.datav.dto.yubei;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-13 17:35
 * @Version: 1.0
 */

@Api(value = "数据汇总")
@Data
@Document(collection  = "1805133676745330689")
public class SummaryYuBeiDto implements Serializable {
    private static final long serialVersionUID = 7523059264202861246L;

    @ApiModelProperty("出租车数量")
    private String chuZuCheShuLiang;
    @ApiModelProperty("驾驶员总数")
    private String jiaShiYuanZongShu;
    @ApiModelProperty("违章总数")
    private String weiZhangZongShu;
    @ApiModelProperty("事故总数")
    private String shiGuZongShu;
    @ApiModelProperty("投诉量")
    private String touSuLiang;
}
