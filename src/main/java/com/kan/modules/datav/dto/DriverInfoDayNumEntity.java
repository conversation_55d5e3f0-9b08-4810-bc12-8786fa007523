package com.kan.modules.datav.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kan.common.utils.Constant;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@TableName("t_driver_day_num")
public class DriverInfoDayNumEntity{
    @TableId
    public Long id;
    private String deptCode;
    private Integer driverNum;
    private String dayTime;
}
