package com.kan.modules.datav.dto.banan;

import com.kan.common.utils.Constant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023-12-13 17:35
 * @Version: 1.0
 */

@Api(value = "数据汇总")
@Data
@Document(collection  = Constant.SUM)
public class SummaryBaNanDto implements Serializable {
    private static final long serialVersionUID = 7523059264202861246L;

    @ApiModelProperty("出租车数量")
    private String chuZuCheShuLiang;
    @ApiModelProperty("驾驶员总数")
    private String jiaShiYuanZongShu;
    @ApiModelProperty("违章总数")
    private String weiZhangZongShu;
    @ApiModelProperty("事故总数")
    private String shiGuZongShu;
    @ApiModelProperty("投诉量")
    private String touSuLiang;

    public static SummaryBaNanDto getSummaryBaNanDto() {
        SummaryBaNanDto summaryBaNanDto=new SummaryBaNanDto();
        summaryBaNanDto.setJiaShiYuanZongShu("0");
        summaryBaNanDto.setChuZuCheShuLiang("0");
        summaryBaNanDto.setTouSuLiang("0");
        summaryBaNanDto.setShiGuZongShu("0");
        summaryBaNanDto.setWeiZhangZongShu("0");
        return summaryBaNanDto;
    }
}
