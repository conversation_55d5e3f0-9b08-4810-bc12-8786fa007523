package com.kan.modules.datav.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.kan.common.utils.Constant;
import com.kan.common.utils.Result;
import com.kan.modules.datav.dto.SubscribeReq;
import com.kan.modules.datav.dto.req.DataReq;
import com.kan.modules.datav.dto.req.WagesReq;
import com.kan.modules.datav.service.DataBoardService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/data")
public class DataBoardController {

    @Resource
    DataBoardService dataBoardService;

    /**
     * 巴南汇总数据
     * @param dataReq 接收参数
     * @return 返回值
     */
    @PostMapping("getSummary")
    public Result getSummary(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.getSummary(dataReq));
    }

    /**
     * 巴南车辆发车数量统计
     * @param dataReq 接收参数
     * @return 返回值
     */
    @PostMapping("getCarYear")
    public Result getCarYear(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.getCarYear(dataReq));
    }
    /**
     * 巴南入职统计
     * @param dataReq 接收参数
     * @return 返回值
     */
    @PostMapping("getAddDriverYear")
    public Result getAddDriverYear(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.getDriverYear(dataReq));
    }
    /**
     * 驾驶员年龄段
     * @return 返回值
     */
    @PostMapping("getDriverAge")
    public Result getDriverAge(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.getDriverAge(dataReq));
    }
//    /**
//     * 巴南离职统计
//     * @param dataReq 接收参数
//     * @return 返回值
//     */
//    @PostMapping("getQuitDriverYear")
//    public Result getQuitDriverYear(@RequestBody DataReq dataReq) {
//        return new Result().data(dataBoardService.getDriverYear(dataReq,Constant.DOWN_DRIVER));
//    }

    /**
     * 巴南投诉台账统计
     * @return 返回值
     */
    @PostMapping("getComplaint")
    public Result getComplaint(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.getComplaint(dataReq));
    }

    /**
     * 工资统计（前端未调用）
     * @return 返回值
     */
    @PostMapping("getMoney")
    public Result getMoney() {
        return new Result().data(dataBoardService.getMoney());
    }

    /**
     * 巴南月度事故列表
     * @return 返回值
     */
    @PostMapping("getHidden")
    public Result getHidden(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.getHiddenNew(dataReq));
    }
    /**
     * 巴南事故统计
     * @return 返回值
     */
    @PostMapping("getAccident")
    public Result getAccident(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.getAccident(dataReq));
    }

    /**
     * 巴南荣誉墙
     * @return 返回值
     */
    @PostMapping("getHonor")
    public Result getHonor(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.getHonor(dataReq));
    }


    /**
     * 工资打印
     * @return 返回值
     */
    @PostMapping("getWagesList")
    public Result getWagesList(@RequestBody WagesReq dataReq) {
        return new Result().data(dataBoardService.getWagesList(dataReq));
    }

    /**
     * 出租车分公司月检、季检、万公里
     * @return 返回值
     */
    @PostMapping("monthInspection")
    public Result monthInspection(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.monthInspection(dataReq));
    }
    /**
     * 在线
     * @return 返回值
     */
    @PostMapping("onLine")
    public Result onLine(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.onLine(dataReq));
    }
    /**
     * 出租车分公司月检、季检、万公里
     * @return 返回值
     */
    @PostMapping("carType")
    public Result carType(@RequestBody DataReq dataReq) {
        return new Result().data(dataBoardService.carType(dataReq));
    }
    @PostMapping("getChuZuPeople")
    public Result getChuZuPeople() {
        return new Result().data(dataBoardService.getChuZuPeople());
    }
    @PostMapping("/weather")
    public Result weatherNow(){
        /* district_id 城市编码：
         500100重庆  500101万州  500102涪陵  500103渝中  500104大渡口  500105江北  500106沙坪坝  500107九龙坡
         500108南岸  500109北碚  500110綦江  500111大足  500112渝北  500113巴南  500114黔江
         500115长寿  500116江津  500117合川  500118永川  500119南川  500120璧山  500151铜梁  500152潼南
         500153荣昌  500154开州  500155梁平  500156武隆  500229城口  500230丰都  500231垫江  500233忠县
         500235云阳  500236奉节  500237巫山  500238巫溪  500240石柱  500241秀山  500242酉阳  500243彭水
         */
        Map paraMap = new HashMap();
        RestTemplate restTemplate = new RestTemplate();
        String s = restTemplate.getForObject("https://api.map.baidu.com/weather/v1/?district_id=500112&data_type=now" +
                "&ak=8GKw6OyRvwdLS9ZgClqf2osb18otuk56",String.class, paraMap);
        return new Result().data(s);
    }
    @PostMapping("saveSubscribe")
    public Result saveSubscribe(@RequestBody SubscribeReq subscribeReq) {
        return new Result().data(dataBoardService.saveSubscribe(subscribeReq));
    }
}
