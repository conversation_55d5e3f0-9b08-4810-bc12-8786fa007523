package com.kan.modules.datav.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.kan.common.utils.Constant;
import com.kan.common.utils.PlatHttpUtil;
import com.kan.modules.datav.CarLineMapper;
import com.kan.modules.datav.dto.*;
import com.kan.modules.datav.dto.banan.AccidentBaNanDto;
import com.kan.modules.datav.dto.banan.ComplaintBaNanDto;
import com.kan.modules.datav.dto.banan.HiddenDtoBaNanNew;
import com.kan.modules.datav.dto.banan.SummaryBaNanDto;
import com.kan.modules.datav.dto.beibei.*;
import com.kan.modules.datav.dto.chuzuche.AccidentChuZuCheDto;
import com.kan.modules.datav.dto.chuzuche.ComplaintChuZuCheDto;
import com.kan.modules.datav.dto.chuzuche.ComplaintChuZuCheOpenDto;
import com.kan.modules.datav.dto.chuzuche.HiddenDtoChuZuCheNew;
import com.kan.modules.datav.dto.inspect.MonthInspectDto;
import com.kan.modules.datav.dto.inspect.MonthYouInspectDto;
import com.kan.modules.datav.dto.inspect.QuarterInspectDto;
import com.kan.modules.datav.dto.inspect.TenInspectDto;
import com.kan.modules.datav.dto.req.DataReq;
import com.kan.modules.datav.dto.req.WagesReq;
import com.kan.modules.datav.dto.resp.DriverDepartResp;
import com.kan.modules.datav.dto.resp.InspectionResp;
import com.kan.modules.datav.dto.resp.SubListResp;
import com.kan.modules.datav.dto.resp.SummaryResp;
import com.kan.modules.datav.dto.yubei.AccidentYuBeiDto;
import com.kan.modules.datav.dto.yubei.ComplaintYuBeiDto;
import com.kan.modules.datav.dto.yubei.HiddenDtoYuBeiNew;
import com.kan.modules.datav.mapper.DriverInfoDayNumMapper;
import com.kan.modules.datav.mapper.SubscribeAllMapper;
import com.kan.modules.datav.mapper.SubscribeMapper;
import com.kan.modules.datav.service.DataBoardService;
import com.kan.modules.datav.utils.CarTypeNumMapper;
import com.kan.modules.datav.utils.CreateQuery;
import com.kan.modules.datav.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class DataBoardServiceImpl implements DataBoardService {
    @Resource
    CarLineMapper carLineMapper;
    @Resource
    CarTypeNumMapper carTypeNumMapper;
    @Resource
    SubscribeMapper subscribeMapper;
    @Resource
    SubscribeAllMapper subscribeAllMapper;
    @Resource
    DriverInfoDayNumMapper driverInfoDayNumMapper;

    //巴南、北碚、出租车、渝北
    public static final String[] DATA_CODE = {
            "4ebcb37f26de278c13ae63d669adb7a1",
            "2e48d8e7713efe9a519de0fc9628ac1c",
            "883e79b028a7ab36330a9a07e3c6ecf6",
            "db320270101159a4688a17f0c3613a79"};
    //民安车队、民成车队、民祥车队、民丰车队、银河车队
    public static final String[] CZC_SON_CODE = {
            "0c256b8ac0ad20025edc029f83d500ef",
            "4af07c450a0fd8d1f024b5d98a33abc2",
            "7132e13bdd1289ed0007a90e9c78b546",
            "883e79b028a7ab36330a9a07e3c6ecf6",
            "71c70ee809f40cef46c3b0d54fcc6d78",
            "bb522de1a9a807090c1ab3f4741b07ef"};
    public static final String[] BB_SON_CODE = {
            "732978aafa110dfa2b6091768fd9ced6",
            "d75fdf5089e20236d62565808c39c731",
            "2e48d8e7713efe9a519de0fc9628ac1c"};
    public static final String[] ALL_CODE = {
            "0c256b8ac0ad20025edc029f83d500ef",
            "4af07c450a0fd8d1f024b5d98a33abc2",
            "732978aafa110dfa2b6091768fd9ced6",
            "7132e13bdd1289ed0007a90e9c78b546",
            "71c70ee809f40cef46c3b0d54fcc6d78",
            "bb522de1a9a807090c1ab3f4741b07ef",
            "4ebcb37f26de278c13ae63d669adb7a1",
            "2e48d8e7713efe9a519de0fc9628ac1c",
            "883e79b028a7ab36330a9a07e3c6ecf6",
            "db320270101159a4688a17f0c3613a79"};
    @Resource
    MongoTemplate mongoTemplate;

    @Override
    public List<SummaryResp> getSummary(DataReq dataReq) {
        SummaryBaNanDto all;
        if (ObjectUtils.isEmpty(dataReq.getDeptCode())) {
            all = new SummaryBaNanDto();
            all.setJiaShiYuanZongShu("0");
            all.setChuZuCheShuLiang("0");
            all.setTouSuLiang("0");
            all.setShiGuZongShu("0");
            all.setWeiZhangZongShu("0");
            Arrays.stream(DATA_CODE).forEach(code -> {
                List<CarLineEntity> carLineEntities = carLineMapper.selectByDept(code);
                SummaryBaNanDto fenGongSi = mongoTemplate.findOne(CreateQuery.createCriteria("fenGongSi", code), SummaryBaNanDto.class);
                all.setShiGuZongShu(String.valueOf(Integer.parseInt(all.getShiGuZongShu()) + Integer.parseInt(fenGongSi.getShiGuZongShu())));
                if (!ObjectUtils.isEmpty(carLineEntities)) {
                    all.setChuZuCheShuLiang(String.valueOf(Integer.parseInt(all.getChuZuCheShuLiang())
                            + carLineEntities.get(carLineEntities.size() - 1).getTotalCount()));
                    all.setTouSuLiang(String.valueOf(Integer.parseInt(all.getTouSuLiang())
                            + carLineEntities.get(carLineEntities.size() - 1).getOnLine()));
                }
                all.setJiaShiYuanZongShu(String.valueOf(Integer.parseInt(all.getJiaShiYuanZongShu()) + Integer.parseInt(fenGongSi.getJiaShiYuanZongShu())));
            });
        } else {
            all = mongoTemplate.findOne(CreateQuery.createCriteria("fenGongSi", dataReq.getDeptCode()), SummaryBaNanDto.class);
            List<CarLineEntity> carLineEntities = carLineMapper.selectByDept(dataReq.getDeptCode());
            if (!ObjectUtils.isEmpty(carLineEntities)) {
                all.setChuZuCheShuLiang(String.valueOf(Integer.parseInt(all.getChuZuCheShuLiang())
                        + carLineEntities.get(carLineEntities.size() - 1).getTotalCount()));
                all.setTouSuLiang(String.valueOf(Integer.parseInt(all.getTouSuLiang())
                        + carLineEntities.get(carLineEntities.size() - 1).getOnLine()));
            }
        }
        List<SummaryResp> resp = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            SummaryResp summaryResp = new SummaryResp();
            if (i == 0) {
                summaryResp.setLabel("事故总数");
                summaryResp.setValue(Integer.parseInt(all.getShiGuZongShu()));
                summaryResp.setMax("次");
            } else if (i == 1) {
                summaryResp.setLabel("在线数");
                summaryResp.setValue(Integer.parseInt(all.getTouSuLiang()));
                summaryResp.setMax("辆");
            } else if (i == 2) {
                summaryResp.setLabel("出租车数量");
                summaryResp.setValue(Integer.parseInt(all.getChuZuCheShuLiang()));
                summaryResp.setMax("辆");
            } else {
                summaryResp.setLabel("驾驶员总数");
                summaryResp.setValue(Integer.parseInt(all.getJiaShiYuanZongShu()));
                summaryResp.setMax("人");
            }
            resp.add(summaryResp);
        }
        return resp;
    }

    @Override
    public DriverDepartResp getCarYear(DataReq dataReq) {
        List<DriverInfoDayNumResp> driverInfoDayNumDtos = driverInfoDayNumMapper.selectByDeptId(dataReq.getDeptCode());
        DriverDepartResp driverDepartResp=new DriverDepartResp();
        List<String> month = new ArrayList<>();
        List<Integer> ruzhi = new ArrayList<>();
        for (DriverInfoDayNumResp driverInfoDayNumDto : driverInfoDayNumDtos) {
            month.add(driverInfoDayNumDto.getDayTime());
            ruzhi.add(driverInfoDayNumDto.getNum());
        }
        driverDepartResp.setMonth(month);
        driverDepartResp.setRuzhi(ruzhi);
        return driverDepartResp;
    }
//    @Override
//    public SubListResp getCarYear(DataReq dataReq) {
//        SimpleDateFormat format=new SimpleDateFormat("MM月dd日");
//        String format1 = format.format(new Date());
//        SubListResp subListResp = new SubListResp();
//        List<SubscribeDto> carYearDtoList;
//        if (ObjectUtils.isEmpty(dataReq.getDeptCode())){
//            carYearDtoList = subscribeMapper.getListAll();
//        }else {
//            carYearDtoList = subscribeMapper.getListByDate(dataReq.getDeptCode());
//        }
//        List<String> month = new ArrayList<>();
//        List<Double> ruzhi = new ArrayList<>();
//        List<Double> lizhi = new ArrayList<>();
//        for (SubscribeDto data : carYearDtoList) {
//            if (data.getFaCheRiQi().equals(format1)){
//                continue;
//            }
//            month.add(data.getFaCheRiQi());
//            ruzhi.add(data.getRuZhi());
//            lizhi.add(data.getLiZhi());
//        }
//        subListResp.setRuzhi(ruzhi);
//        subListResp.setMonth(month);
//        subListResp.setLizhi(lizhi);
//        return subListResp;
//    }

//    @Override
//    public DriverDepartResp getCarYear(DataReq dataReq) {
//        DriverDepartResp driverDepartResp = new DriverDepartResp();
//        SimpleDateFormat format = new SimpleDateFormat("yyyy");
//        SimpleDateFormat end = new SimpleDateFormat("yyyy-MM-dd");
//        String subStrExpress = "substr(faCheRiQi, 0, 7)";
//        Criteria criteria = new Criteria();
//        if (ObjectUtils.isEmpty(dataReq.getDeptCode())) {
//            List<String> carCode = Arrays.asList(ALL_CODE);
//            criteria.and("suoShuGongSiJiCheDui").in(carCode);
//        } else {
//            //过滤条件
//            if (dataReq.getDeptCode().equals(DATA_CODE[2])) {
//                List<String> stream = Arrays.asList(CZC_SON_CODE);
//                criteria.and("suoShuGongSiJiCheDui").in(stream);
//            }else if(dataReq.getDeptCode().equals(DATA_CODE[1])){
//                List<String> stream = Arrays.asList(BB_SON_CODE);
//                criteria.and("suoShuGongSiJiCheDui").in(stream);
//            } else {
//                criteria.and("suoShuGongSiJiCheDui").in(dataReq.getDeptCode());
//            }
//        }
//
//        criteria.and("faCheRiQi").gte(format.format(new Date()) + "-01-01").lte(end.format(new Date()));
//        //筛选时间范围
//        TypedAggregation<CarYearDto> aggregation = Aggregation.newAggregation(CarYearDto.class,
//                Aggregation.match(criteria),
//                //存储时间格式为string类型
//                Aggregation.project().andExpression(subStrExpress).as("faCheRiQi"),
//                Aggregation.group("faCheRiQi").count().as("count"),
//                //格式化输出为：faCheRiQi，count--原(_id, count)
//                Aggregation.project("faCheRiQi", "count").and("faCheRiQi").previousOperation(),
//                //排序
//                Aggregation.sort(Sort.Direction.ASC, "faCheRiQi")
//        );
//        AggregationResults<CarYearDto> aggregate = mongoTemplate.aggregate(aggregation, CarYearDto.class);
//        List<String> dateList = getDateList();
//        List<String> month = new ArrayList<>();
//        List<Integer> ruzhi = new ArrayList<>();
//        for (String data : dateList) {
//            month.add(data);
//            int ruzhiCount = 0;
//            for (CarYearDto driverRuZhiDto : aggregate) {
//                if (driverRuZhiDto.getFaCheRiQi().contains(data)) {
//                    ruzhiCount = ruzhiCount + driverRuZhiDto.getCount();
//                }
//            }
//            ruzhi.add(ruzhiCount);
//        }
//        driverDepartResp.setRuzhi(ruzhi);
//        driverDepartResp.setMonth(month);
//        return driverDepartResp;
//    }

    @Override
    public DriverDepartResp getDriverYear(DataReq dataReq) {
        DriverDepartResp driverDepartResp = new DriverDepartResp();
        List<String> dateList = getDateList();
        List<String> month = new ArrayList<>();
        List<Integer> ruzhi = new ArrayList<>();
        List<Integer> lizhi = new ArrayList<>();
        for (String data : dateList) {
            month.add(data);
            int lizhiCount = 0;
            int ruzhiCount = 0;
            //离职
            AggregationResults<DriverDepartDto> driverLiZhi = getDriverRuLiZhi(dataReq, Constant.ADD_DRIVER);
            for (DriverDepartDto mappedResult : driverLiZhi.getMappedResults()) {
                if (mappedResult.getRuLiZhiShiJian().contains(data)) {
                    ruzhiCount = ruzhiCount + mappedResult.getCount();
                }
            }
            AggregationResults<DriverDepartDto> driverRuLiZhi = getDriverRuLiZhi(dataReq, Constant.DOWN_DRIVER);
            for (DriverDepartDto mappedResult : driverRuLiZhi.getMappedResults()) {
                if (mappedResult.getRuLiZhiShiJian().contains(data)) {
                    lizhiCount = lizhiCount + mappedResult.getCount();
                }
            }
            ruzhi.add(ruzhiCount);
            lizhi.add(lizhiCount);
            driverDepartResp.setRuzhi(ruzhi);
            driverDepartResp.setLizhi(lizhi);
        }
        driverDepartResp.setMonth(month);
        return driverDepartResp;
    }

    @Override
    public DriverDepartResp onLine(DataReq dataReq) {
        DriverDepartResp driverDepartResp = new DriverDepartResp();
        List<String> time = new ArrayList<>();
        List<Integer> totalCount = new ArrayList<>();
        List<Integer> onLine = new ArrayList<>();
        List<CarLineEntity> carLineEntities;
        if (!ObjectUtils.isEmpty(dataReq.getDeptCode())) {
            carLineEntities = carLineMapper.selectByDept(dataReq.getDeptCode());
        } else {
            carLineEntities = carLineMapper.selectTotalCount();
        }
        for (CarLineEntity carLineEntity : carLineEntities) {
            if (carLineEntity.getCreateTime().equals("10")) {
                time.add(carLineEntity.getCreateTime() + "时");
            } else if (carLineEntity.getCreateTime().equals("00")) {
                time.add("24时");
            } else {
                time.add(carLineEntity.getCreateTime().replace("0", "") + "时");
            }
            totalCount.add(carLineEntity.getTotalCount());
            onLine.add(carLineEntity.getOnLine());
        }
        driverDepartResp.setMonth(time);
        driverDepartResp.setRuzhi(totalCount);
        driverDepartResp.setLizhi(onLine);
        return driverDepartResp;
    }

    @Override
    public List<CarTypeNumDto> carType(DataReq dataReq) {
        return carTypeNumMapper.getCarTypeNum(dataReq.getDeptCode());
    }

    @Override
    public DriverDepartResp getChuZuPeople() {
        DriverDepartResp driverDepartResp = new DriverDepartResp();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String format1 = sdf.format(new Date());
        format1 = "2024-05";
        List<String> dateList = new ArrayList<>();
        dateList.add("民安");
        dateList.add("民丰");
        dateList.add("民成");
        dateList.add("银河");
        dateList.add("民祥");
        List<String> month = new ArrayList<>();
        List<Integer> ruzhi = new ArrayList<>();
        List<Integer> lizhi = new ArrayList<>();
        for (String data : dateList) {
            DataReq dataReq = new DataReq();
            String deptCode = "";
            switch (data) {
                case "民安":
                    deptCode = "0c256b8ac0ad20025edc029f83d500ef";
                    break;
                case "民丰":
                    deptCode = "71c70ee809f40cef46c3b0d54fcc6d78";
                    break;
                case "民成":
                    deptCode = "4af07c450a0fd8d1f024b5d98a33abc2";
                    break;
                case "银河":
                    deptCode = "bb522de1a9a807090c1ab3f4741b07ef";
                    break;
                default:
                    deptCode = "7132e13bdd1289ed0007a90e9c78b546";
                    break;
            }
            dataReq.setDeptCode(deptCode);
            month.add(data);
            int lizhiCount = 0;
            int ruzhiCount = 0;
            //离职
            AggregationResults<DriverDepartDto> driverLiZhi = getDriverRuLiZhi(dataReq, Constant.ADD_DRIVER);
            for (DriverDepartDto mappedResult : driverLiZhi.getMappedResults()) {
                if (mappedResult.getRuLiZhiShiJian().contains(format1)) {
                    ruzhiCount = ruzhiCount + mappedResult.getCount();
                }
            }
            AggregationResults<DriverDepartDto> driverRuLiZhi = getDriverRuLiZhi(dataReq, Constant.DOWN_DRIVER);
            for (DriverDepartDto mappedResult : driverRuLiZhi.getMappedResults()) {
                if (mappedResult.getRuLiZhiShiJian().contains(format1)) {
                    lizhiCount = lizhiCount + mappedResult.getCount();
                }
            }
            ruzhi.add(ruzhiCount);
            lizhi.add(lizhiCount);
            driverDepartResp.setRuzhi(ruzhi);
            driverDepartResp.setLizhi(lizhi);
        }
        driverDepartResp.setMonth(month);
        return driverDepartResp;
    }

    @Override
    public Boolean saveSubscribe(SubscribeReq subscribeReq) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SubscribeAll subscribeAll = new SubscribeAll();
        BeanUtils.copyProperties(subscribeReq,subscribeAll);
        subscribeAll.setCreateTime(sdf2.format(new Date()));
        String format = sdf.format(new Date());
        subscribeReq.setCreateTime(format);
        String s = PlatHttpUtil.sendGetParams("http://36.134.192.18:48081/getCar?carNo=" + subscribeReq.getCarNo(), null, "UTF-8", "1");
        String replace = s.replace("[", "").replace("]", "");
        SubscribeResp.CarInfo carInfo = JSONObject.parseObject(replace, SubscribeResp.CarInfo.class);
        if (!ObjectUtils.isEmpty(carInfo)) {
            String companyName=carInfo.getCompanyName();
            subscribeAll.setCompanyName(companyName);
            subscribeAllMapper.insert(subscribeAll);
            if (companyName.equals("巴南分公司")) {
                companyName = DATA_CODE[0];
            } else if (companyName.equals("北碚分公司")) {
                companyName = DATA_CODE[1];
            } else if (companyName.equals("出租分公司")) {
                companyName = DATA_CODE[2];
            } else if (companyName.equals("渝北分公司")) {
                companyName = DATA_CODE[3];
            }else{

                return true;
            }
            LambdaQueryWrapper<SubscribeReq> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SubscribeReq::getCompanyName, companyName);
            queryWrapper.eq(SubscribeReq::getCreateTime, format);

            SubscribeReq old = subscribeMapper.selectOne(queryWrapper);
            if (!ObjectUtils.isEmpty(old)) {
                BigDecimal oldDecimal = new BigDecimal(old.getIncome().toString());
                BigDecimal newDecimal = new BigDecimal(subscribeReq.getIncome().toString());
                old.setIncome(oldDecimal.add(newDecimal).doubleValue());
                subscribeMapper.updateById(old);
            } else {
                subscribeReq.setCompanyName(companyName);
                subscribeMapper.insert(subscribeReq);
            }

        }
        return null;
    }

    @Override
    public DriverDepartResp getComplaint(DataReq dataReq) {
        DriverDepartResp driverDepartResp = new DriverDepartResp();
        List<String> dateList = getDateList();
        List<String> month = new ArrayList<>();
        List<Integer> ruzhi = new ArrayList<>();
        List<ComplaintBaNanDto> complaintFun;
        //全部
        if (ObjectUtils.isEmpty(dataReq.getDeptCode())) {
            complaintFun = (List<ComplaintBaNanDto>) getComplaintFun(ComplaintBaNanDto.class);
            //北碚数据
            List<ComplaintBeibeiDto> beiBei = (List<ComplaintBeibeiDto>) getComplaintFun(ComplaintBeibeiDto.class);
            List<ComplaintBaNanDto> list = (List<ComplaintBaNanDto>) mergeList(complaintFun, beiBei, ComplaintBaNanDto.class);
            //民鑫数据
            List<ComplaintMingXinDto> mingXin = (List<ComplaintMingXinDto>) getComplaintFun(ComplaintMingXinDto.class);
            List<ComplaintBaNanDto> list1 = (List<ComplaintBaNanDto>) mergeList(list, mingXin, ComplaintBaNanDto.class);
            //出租车内部
            List<ComplaintBaNanDto> chuZuCheIn = (List<ComplaintBaNanDto>) getComplaintFun(ComplaintChuZuCheDto.class);
            List<ComplaintBaNanDto> list2 = (List<ComplaintBaNanDto>) mergeList(list1, chuZuCheIn, ComplaintBaNanDto.class);
            //出租车外部数据
            List<ComplaintBaNanDto> chuZuCheOpen = (List<ComplaintBaNanDto>) getComplaintFun(ComplaintChuZuCheOpenDto.class);
            List<ComplaintBaNanDto> list3 = (List<ComplaintBaNanDto>) mergeList(list2, chuZuCheOpen, ComplaintBaNanDto.class);
            //渝北数据
            List<ComplaintBaNanDto> yuBei = (List<ComplaintBaNanDto>) getComplaintFun(ComplaintYuBeiDto.class);
            complaintFun = (List<ComplaintBaNanDto>) mergeList(list3, yuBei, ComplaintBaNanDto.class);
        } else {
            if (dataReq.getDeptCode().equals(DATA_CODE[0])) {
                //巴南数据
                complaintFun = (List<ComplaintBaNanDto>) getComplaintFun(ComplaintBaNanDto.class);
            } else if (dataReq.getDeptCode().equals(DATA_CODE[1])) {
                //北碚数据
                List<ComplaintBeibeiDto> beiBei = (List<ComplaintBeibeiDto>) getComplaintFun(ComplaintBeibeiDto.class);
                //民鑫数据
                List<ComplaintMingXinDto> mingXin = (List<ComplaintMingXinDto>) getComplaintFun(ComplaintMingXinDto.class);
                complaintFun = (List<ComplaintBaNanDto>) mergeList(beiBei, mingXin, ComplaintBaNanDto.class);
            } else if (dataReq.getDeptCode().equals(DATA_CODE[2])) {
                //出租车内部数据
                complaintFun = (List<ComplaintBaNanDto>) getComplaintFun(ComplaintChuZuCheDto.class);
                //出租车外部数据
                List<ComplaintBaNanDto> chuZuCheOpen = (List<ComplaintBaNanDto>) getComplaintFun(ComplaintChuZuCheOpenDto.class);
                complaintFun = (List<ComplaintBaNanDto>) mergeList(complaintFun, chuZuCheOpen, ComplaintBaNanDto.class);
            } else {
                //渝北数据
                complaintFun = (List<ComplaintBaNanDto>) getComplaintFun(ComplaintYuBeiDto.class);
            }
        }
        List<ComplaintBaNanDto> objects = (List<ComplaintBaNanDto>) coverList(complaintFun, ComplaintBaNanDto.class);
        for (String data : dateList) {
            month.add(data);
            int ruzhiCount = 0;
            for (ComplaintBaNanDto driverRuZhiDto : objects) {
                if (driverRuZhiDto.getShiFaShiJian().contains(data)) {
                    ruzhiCount = ruzhiCount + driverRuZhiDto.getCount();
                }
            }
            ruzhi.add(ruzhiCount);
        }
        driverDepartResp.setRuzhi(ruzhi);
        driverDepartResp.setMonth(month);
        return driverDepartResp;
    }

    private List<?> getComplaintFun(Class<?> c) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        SimpleDateFormat end = new SimpleDateFormat("yyyy-MM-dd");
        Criteria criteria = new Criteria();
        //过滤条件
        criteria.and("shiFaShiJian").gte(format.format(new Date()) + "-01-01").lte(end.format(new Date()));
        //筛选时间范围
        TypedAggregation<?> aggregation = Aggregation.newAggregation(c,
                Aggregation.match(criteria),
                Aggregation.project("shiFaShiJian")
                        .andExpression("{$dateToString: {date: { $add: {'shiFaShiJian', [0]} }, format: '%Y%m'}}", new Date(28800000)),
                Aggregation.group("shiFaShiJian").count().as("count"),
                //格式化输出为：faCheRiQi，count--原(_id, count)
                Aggregation.project("shiFaShiJian", "count").and("shiFaShiJian").previousOperation(),
                Aggregation.sort(Sort.Direction.ASC, "shiFaShiJian")
        );
        return mongoTemplate.aggregate(aggregation, c).getMappedResults();
    }

    @Override
    public DriverDepartResp getMoney() {
        DriverDepartResp driverDepartResp = new DriverDepartResp();
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        SimpleDateFormat end = new SimpleDateFormat("yyyy-MM-dd");
        Criteria criteria = new Criteria();
        //过滤条件
        criteria.and("tongJiYueFen").gte(format.format(new Date()) + "-01-01").lte(end.format(new Date()));
        List<MoneyDto> aggregate = mongoTemplate.find(Query.query(criteria), MoneyDto.class);
        List<String> dateList = getDateList();
        List<String> month = new ArrayList<>();
        List<Integer> ruzhi = new ArrayList<>();
        for (String data : dateList) {
            month.add(data);
            int ruzhiCount = 0;
            for (MoneyDto dto : aggregate) {
                if (dto.getTongJiYueFen().contains(data)) {
                    ruzhiCount = ruzhiCount + dto.getGongZiShu();
                }
            }
            ruzhi.add(ruzhiCount);
        }
        driverDepartResp.setRuzhi(ruzhi);
        driverDepartResp.setMonth(month);
        return driverDepartResp;
    }

//    @Override
//    public DriverDepartResp getHidden(DataReq dataReq) {
//        DriverDepartResp driverDepartResp = new DriverDepartResp();
//        SimpleDateFormat format = new SimpleDateFormat("yyyy");
//        SimpleDateFormat end = new SimpleDateFormat("yyyy-MM-dd");
//        Criteria criteria = new Criteria();
//        //过滤条件
//        criteria.and("shiGuShiJian").gte(format.format(new Date()) + "-01-01").lte(end.format(new Date()));
//        //筛选时间范围
//        TypedAggregation<HiddenDto> aggregation = Aggregation.newAggregation(HiddenDto.class,
//                Aggregation.match(criteria),
//                Aggregation.project("shiGuShiJian")
//                        .andExpression("{$dateToString: {date: { $add: {'shiGuShiJian', [0]} }, format: '%Y%m'}}", new Date(28800000)),
//                Aggregation.group("shiGuShiJian").count().as("count"),
//                //格式化输出为：faCheRiQi，count--原(_id, count)
//                Aggregation.project("shiGuShiJian", "count").and("shiGuShiJian").previousOperation(),
//                Aggregation.sort(Sort.Direction.ASC, "shiGuShiJian")
//        );
//        AggregationResults<HiddenDto> aggregate = mongoTemplate.aggregate(aggregation, HiddenDto.class);
//        List<String> dateList = getDateList();
//        List<String> month = new ArrayList<>();
//        List<Integer> ruzhi = new ArrayList<>();
//        for (String data : dateList) {
//            month.add(data);
//            int ruzhiCount = 0;
//            for (HiddenDto dto : aggregate) {
//                if (dto.getShiGuShiJian().contains(data)) {
//                    ruzhiCount = ruzhiCount + dto.getCount();
//                }
//            }
//            ruzhi.add(ruzhiCount);
//        }
//        driverDepartResp.setRuzhi(ruzhi);
//        driverDepartResp.setMonth(month);
//        return driverDepartResp;
//    }

    @Override
    public List<HiddenDtoNew> getHiddenNew(DataReq dataReq) {
        List<HiddenDtoNew> hidden;
        //全部
        if (ObjectUtils.isEmpty(dataReq.getDeptCode())) {
            List<HiddenDtoBaNanNew> baNan = (List<HiddenDtoBaNanNew>) getHidden(HiddenDtoBaNanNew.class);
            List<HiddenDtoBeiBeiNew> beiBei = (List<HiddenDtoBeiBeiNew>) getHidden(HiddenDtoBeiBeiNew.class);
            List<HiddenDtoNew> list1 = (List<HiddenDtoNew>) mergeList(beiBei, baNan, HiddenDtoNew.class);
            List<HiddenDtoMingXinNew> mingXin = (List<HiddenDtoMingXinNew>) getHidden(HiddenDtoMingXinNew.class);
            List<HiddenDtoNew> list2 = (List<HiddenDtoNew>) mergeList(list1, mingXin, HiddenDtoNew.class);
            List<HiddenDtoChuZuCheNew> chuZuChe = (List<HiddenDtoChuZuCheNew>) getHidden(HiddenDtoChuZuCheNew.class);
            List<HiddenDtoNew> list3 = (List<HiddenDtoNew>) mergeList(list2, chuZuChe, HiddenDtoNew.class);
            List<HiddenDtoYuBeiNew> yuBei = (List<HiddenDtoYuBeiNew>) getHidden(HiddenDtoYuBeiNew.class);
            hidden = (List<HiddenDtoNew>) mergeList(list3, yuBei, HiddenDtoNew.class);
        } else {
            if (dataReq.getDeptCode().equals(DATA_CODE[0])) {
                hidden = (List<HiddenDtoNew>) getHidden(HiddenDtoBaNanNew.class);
            } else if (dataReq.getDeptCode().equals(DATA_CODE[1])) {
                List<HiddenDtoBeiBeiNew> beiBei = (List<HiddenDtoBeiBeiNew>) getHidden(HiddenDtoBeiBeiNew.class);
                List<HiddenDtoMingXinNew> mingXin = (List<HiddenDtoMingXinNew>) getHidden(HiddenDtoMingXinNew.class);
                hidden = (List<HiddenDtoNew>) mergeList(mingXin, beiBei, HiddenDtoNew.class);
            } else if (dataReq.getDeptCode().equals(DATA_CODE[2])) {
                hidden = (List<HiddenDtoNew>) getHidden(HiddenDtoChuZuCheNew.class);
            } else {
                hidden = (List<HiddenDtoNew>) getHidden(HiddenDtoYuBeiNew.class);
            }
        }
        List<HiddenDtoNew> objects = (List<HiddenDtoNew>) coverList(hidden, HiddenDtoNew.class);
        for (HiddenDtoNew hiddenDtoNew : objects) {
            if (ObjectUtils.isEmpty(hiddenDtoNew.getShiGuJingGuo())) {
                hiddenDtoNew.setShiGuJingGuo("无");
            }
        }
        return hidden;
    }

    @Override
    public List<SummaryResp> getDriverAge(DataReq dataReq) {
        List<SummaryResp> summaryResps = new ArrayList<>();
        List<DriverInfoDto> driverAgeFun = (List<DriverInfoDto>) getDriverAgeFun(DriverInfoDto.class, dataReq);
        Integer max = 1;
        if (!ObjectUtils.isEmpty(driverAgeFun)) {
            max = driverAgeFun.get(0).getCount();
        }
        SummaryResp five = new SummaryResp();
        Map<String, Integer> map = new HashMap<>();
        for (DriverInfoDto driverInfoDto : driverAgeFun) {
            if (Integer.parseInt(driverInfoDto.getNianLing()) < 2) {
                map.put(Constant.ARR_AGE[0], driverInfoDto.getCount());
            } else if (driverInfoDto.getNianLing().equals("2")) {
                map.put(Constant.ARR_AGE[1], driverInfoDto.getCount());
            } else if (driverInfoDto.getNianLing().equals("3")) {
                map.put(Constant.ARR_AGE[2], driverInfoDto.getCount());
            } else if (driverInfoDto.getNianLing().equals("4")) {
                map.put(Constant.ARR_AGE[3], driverInfoDto.getCount());
            } else if (driverInfoDto.getNianLing().equals("5")) {
                map.put(Constant.ARR_AGE[4], driverInfoDto.getCount());
            } else if (Integer.parseInt(driverInfoDto.getNianLing()) > 5) {
                five.setValue(five.getValue() + driverInfoDto.getCount());
            }
        }
        map.put(Constant.ARR_AGE[5], five.getValue());
        for (String s : Constant.ARR_AGE) {
            SummaryResp resp = new SummaryResp();
            resp.setLabel(s);
            resp.setMax(max.toString());
            if (ObjectUtils.isEmpty(map.get(s))) {
                resp.setValue(0);
            } else {
                resp.setValue(map.get(s));
            }
            summaryResps.add(resp);
        }
        return summaryResps;
    }

    @Override
    public InspectionResp monthInspection(DataReq dataReq) {
        InspectionResp inspectionResp = new InspectionResp();
        List<String> dateList = getDateList();
        List<String> time = new ArrayList<>();
        List<Integer> month = new ArrayList<>();
        List<Integer> quarter = new ArrayList<>();
        List<Integer> tenThousand = new ArrayList<>();
        for (String data : dateList) {
            time.add(data);
            int monthCount = 0;
            int quarterCount = 0;
            int tenThousandCount = 0;
            //月检
            List<MonthInspectDto> monthResp = (List<MonthInspectDto>) getMonthInspectionFun(MonthInspectDto.class, dataReq);
            List<MonthYouInspectDto> monthInspectionFun = (List<MonthYouInspectDto>) getMonthInspectionFun(MonthYouInspectDto.class, dataReq);
            List<MonthInspectDto> objects = (List<MonthInspectDto>) mergeList(monthResp, monthInspectionFun, MonthInspectDto.class);
            for (MonthInspectDto mappedResult : objects) {
                if (mappedResult.getCreateTime().contains(data)) {
                    monthCount = monthCount + mappedResult.getCount();
                }
            }
            //季检
            List<QuarterInspectDto> quarterResp = (List<QuarterInspectDto>) getMonthInspectionFun(QuarterInspectDto.class, dataReq);
            for (QuarterInspectDto mappedResult : quarterResp) {
                if (mappedResult.getCreateTime().contains(data)) {
                    quarterCount = quarterCount + mappedResult.getCount();
                }
            }
            //万公里
            List<TenInspectDto> tenThousandResp = (List<TenInspectDto>) getMonthInspectionFun(TenInspectDto.class, dataReq);
            for (TenInspectDto mappedResult : tenThousandResp) {
                if (mappedResult.getCreateTime().contains(data)) {
                    tenThousandCount = tenThousandCount + mappedResult.getCount();
                }
            }
            month.add(monthCount);
            quarter.add(quarterCount);
            tenThousand.add(tenThousandCount);
            inspectionResp.setTenThousand(tenThousand);
            inspectionResp.setQuarter(quarter);
            inspectionResp.setMonth(month);
        }
        inspectionResp.setTime(time);
        return inspectionResp;
    }

    private List<?> getMonthInspectionFun(Class<?> c, DataReq dataReq) {
        String subStrExpress = "substr(createTime, 0, 7)";
        Criteria criteria = new Criteria();
        //筛选时间范围
        TypedAggregation<?> aggregation = Aggregation.newAggregation(c,
                Aggregation.match(criteria),
                Aggregation.project("createTime")
                        .andExpression(subStrExpress).as("createTime"),
                Aggregation.group("createTime").count().as("count"),
                //格式化输出为：faCheRiQi，count--原(_id, count)
                Aggregation.project("createTime", "count").and("createTime").previousOperation(),
                Aggregation.sort(Sort.Direction.DESC, "count")
        );
        return mongoTemplate.aggregate(aggregation, c).getMappedResults();
    }

    private List<?> getDriverAgeFun(Class<?> c, DataReq dataReq) {
        String subStrExpress = "substr(nianLing, 0, 1)";
        Criteria criteria = new Criteria();
        if (ObjectUtils.isEmpty(dataReq.getDeptCode())) {
            List<String> carCode = Arrays.asList(ALL_CODE);
            criteria.and("suoShuGongSiJiCheDui").in(carCode);
        } else {
            //过滤条件
            if (dataReq.getDeptCode().equals(DATA_CODE[2])) {
                List<String> stream = Arrays.asList(CZC_SON_CODE);
                criteria.and("suoShuGongSiJiCheDui").in(stream);
            } else if (dataReq.getDeptCode().equals(DATA_CODE[1])) {
                List<String> stream = Arrays.asList(BB_SON_CODE);
                criteria.and("suoShuGongSiJiCheDui").in(stream);
            } else {
                criteria.and("suoShuGongSiJiCheDui").in(dataReq.getDeptCode());
            }
        }
        //筛选时间范围
        TypedAggregation<?> aggregation = Aggregation.newAggregation(c,
                Aggregation.match(criteria),
                Aggregation.project("nianLing")
                        .andExpression(subStrExpress).as("nianLing"),
                Aggregation.group("nianLing").count().as("count"),
                //格式化输出为：faCheRiQi，count--原(_id, count)
                Aggregation.project("nianLing", "count").and("nianLing").previousOperation(),
                Aggregation.sort(Sort.Direction.DESC, "count")
        );
        return mongoTemplate.aggregate(aggregation, c).getMappedResults();
    }

    private List<?> getHidden(Class<?> c) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat end = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Criteria criteria = new Criteria();
        //过滤条件
        criteria.and("shiGuShiJian").gte(format.format(new Date()) + "-01").lte(end.format(new Date()));
        //筛选时间范围
        TypedAggregation<?> aggregation = Aggregation.newAggregation(c,
                Aggregation.match(criteria),
                Aggregation.sort(Sort.Direction.ASC, "shiGuShiJian")
        );
        return mongoTemplate.aggregate(aggregation, c).getMappedResults();
    }

    @Override
    public DriverDepartResp getAccident(DataReq dataReq) {
        DriverDepartResp driverDepartResp = new DriverDepartResp();
        List<AccidentDto> hidden;
        //全部
        if (ObjectUtils.isEmpty(dataReq.getDeptCode())) {
            List<AccidentBaNanDto> baNan = (List<AccidentBaNanDto>) getAccidentFun(AccidentBaNanDto.class);
            List<AccidentBeiBeiDto> beiBei = (List<AccidentBeiBeiDto>) getAccidentFun(AccidentBeiBeiDto.class);
            List<AccidentDto> list1 = (List<AccidentDto>) mergeList(beiBei, baNan, AccidentDto.class);
            List<AccidentMingXinDto> mingXin = (List<AccidentMingXinDto>) getAccidentFun(AccidentMingXinDto.class);
            List<AccidentDto> list2 = (List<AccidentDto>) mergeList(list1, mingXin, AccidentDto.class);
            List<AccidentChuZuCheDto> chuZuChe = (List<AccidentChuZuCheDto>) getAccidentFun(AccidentChuZuCheDto.class);
            List<AccidentDto> list3 = (List<AccidentDto>) mergeList(list2, chuZuChe, AccidentDto.class);
            List<AccidentYuBeiDto> yuBei = (List<AccidentYuBeiDto>) getAccidentFun(AccidentYuBeiDto.class);
            hidden = (List<AccidentDto>) mergeList(list3, yuBei, AccidentDto.class);

        } else {
            if (dataReq.getDeptCode().equals(DATA_CODE[0])) {
                hidden = (List<AccidentDto>) getAccidentFun(AccidentBaNanDto.class);
            } else if (dataReq.getDeptCode().equals(DATA_CODE[1])) {
                List<HiddenDtoBeiBeiNew> beiBei = (List<HiddenDtoBeiBeiNew>) getAccidentFun(AccidentBeiBeiDto.class);
                List<HiddenDtoMingXinNew> mingXin = (List<HiddenDtoMingXinNew>) getAccidentFun(AccidentMingXinDto.class);
                hidden = (List<AccidentDto>) mergeList(mingXin, beiBei, AccidentDto.class);
            } else if (dataReq.getDeptCode().equals(DATA_CODE[2])) {
                hidden = (List<AccidentDto>) getAccidentFun(AccidentChuZuCheDto.class);
            } else {
                hidden = (List<AccidentDto>) getAccidentFun(AccidentYuBeiDto.class);
            }
        }
        List<AccidentDto> objects = (List<AccidentDto>) coverList(hidden, AccidentDto.class);
        List<String> dateList = getDateList();
        List<String> month = new ArrayList<>();
        List<Integer> ruzhi = new ArrayList<>();
        for (String data : dateList) {
            month.add(data);
            int ruzhiCount = 0;
            for (AccidentDto dto : objects) {
                if (!ObjectUtils.isEmpty(dto.getShiGuShiJian()) && dto.getShiGuShiJian().contains(data)) {
                    ruzhiCount = ruzhiCount + dto.getCount();
                }
            }
            ruzhi.add(ruzhiCount);
        }
        driverDepartResp.setRuzhi(ruzhi);
        driverDepartResp.setMonth(month);
        return driverDepartResp;
    }

    private List<?> getAccidentFun(Class<?> c) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        SimpleDateFormat end = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String subStrExpress = "substr(shiGuShiJian, 0, 7)";
        Criteria criteria = new Criteria();
        //过滤条件
        criteria.and("shiGuShiJian").gte(format.format(new Date()) + "-01-01").lte(end.format(new Date()));
        //筛选时间范围
        TypedAggregation<?> aggregation = Aggregation.newAggregation(c,
                Aggregation.match(criteria),
                Aggregation.project("shiGuShiJian")
                        .andExpression(subStrExpress).as("shiGuShiJian"),
                Aggregation.group("shiGuShiJian").count().as("count"),
                //格式化输出为：faCheRiQi，count--原(_id, count)
                Aggregation.project("shiGuShiJian", "count").and("shiGuShiJian").previousOperation(),
                Aggregation.sort(Sort.Direction.ASC, "shiGuShiJian")
        );
        return mongoTemplate.aggregate(aggregation, c).getMappedResults();
    }

    //    private List<?> getAccidentFun(Class<?> c) {
//        SimpleDateFormat format=new SimpleDateFormat("yyyy");
//        SimpleDateFormat end = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        Criteria criteria = new Criteria();
//        //过滤条件
//        criteria.and("shiGuShiJian").gte(format.format(new Date())+"-01-01").lte(end.format(new Date()));
//        //筛选时间范围
//        TypedAggregation<?> aggregation = Aggregation.newAggregation(c,
//                Aggregation.match(criteria),
//                Aggregation.project("shiGuShiJian")
//                        .andExpression("{$dateToString: {date: { $add: {'shiGuShiJian', [0]} }, format: '%Y%m%d'}}", new Date(28800000)),
//                Aggregation.group("shiGuShiJian").count().as("count"),
//                //格式化输出为：faCheRiQi，count--原(_id, count)
//                Aggregation.project("shiGuShiJian", "count").and("shiGuShiJian").previousOperation(),
//                Aggregation.sort(Sort.Direction.ASC, "shiGuShiJian")
//        );
//        return mongoTemplate.aggregate(aggregation, c).getMappedResults();
//    }
    @Override
    public List<SummaryResp> getHonor(DataReq dataReq) {
        List<SummaryResp> resps = new ArrayList<>();
        Criteria criteria = new Criteria();
        if (ObjectUtils.isEmpty(dataReq.getDeptCode())) {
            List<String> carCode = Arrays.asList(ALL_CODE);
            criteria.and("fenGongSi").in(carCode);
        } else {
            //过滤条件
            if (dataReq.getDeptCode().equals(DATA_CODE[2])) {
                List<String> stream = Arrays.asList(CZC_SON_CODE);
                criteria.and("fenGongSi").in(stream);
            } else {
                criteria.and("fenGongSi").in(dataReq.getDeptCode());
            }
        }
        //筛选时间范围
        TypedAggregation<HonorDto> aggregation = Aggregation.newAggregation(HonorDto.class, Aggregation.match(criteria));
        AggregationResults<HonorDto> aggregate = mongoTemplate.aggregate(aggregation, HonorDto.class);
        List<HonorDto> mappedResults = aggregate.getMappedResults();
        if (ObjectUtils.isEmpty(mappedResults)) {
            return null;
        }
        for (HonorDto fileDto : mappedResults) {
            List<FileDto> ziZhiFuJian = fileDto.getZiZhiFuJian();
            for (FileDto dto : ziZhiFuJian) {
                SummaryResp summaryResp = new SummaryResp();
                summaryResp.setMax(dto.getUrl());
                resps.add(summaryResp);
            }
        }
        return resps;
    }

    //获取离职人员
    public AggregationResults<DriverDepartDto> getDriverRuLiZhi(DataReq dataReq, String type) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        SimpleDateFormat end = new SimpleDateFormat("yyyy-MM-dd");
        Criteria criteria = new Criteria();
        String subStrExpress = "substr(ruLiZhiShiJian, 0, 7)";
        //过滤条件
        if (ObjectUtils.isEmpty(dataReq.getDeptCode())) {
            List<String> carCode = Arrays.asList(ALL_CODE);
            criteria.and("suoShuGongSiJiCheDui").in(carCode);
        } else {
            //过滤条件
            if (dataReq.getDeptCode().equals(DATA_CODE[2])) {
                List<String> stream = Arrays.asList(CZC_SON_CODE);
                criteria.and("suoShuGongSiJiCheDui").in(stream);
            } else if (dataReq.getDeptCode().equals(DATA_CODE[1])) {
                List<String> stream = Arrays.asList(BB_SON_CODE);
                criteria.and("suoShuGongSiJiCheDui").in(stream);
            } else {
                criteria.and("suoShuGongSiJiCheDui").in(dataReq.getDeptCode());
            }
        }
        criteria.and("ruLiZhiZhuangTai").is(type);
        criteria.and("ruLiZhiShiJian").gte(format.format(new Date()) + "-01-01").lte(end.format(new Date()));
        //筛选时间范围
        TypedAggregation<DriverDepartDto> aggregation = Aggregation.newAggregation(DriverDepartDto.class,
                Aggregation.match(criteria),
                //存储时间格式为string类型
                Aggregation.project().andExpression(subStrExpress).as("ruLiZhiShiJian"),
                Aggregation.group("ruLiZhiShiJian").count().as("count"),
                //格式化输出为：faCheRiQi，count--原(_id, count)
                Aggregation.project("ruLiZhiShiJian", "count").and("ruLiZhiShiJian").previousOperation(),
                //排序
                Aggregation.sort(Sort.Direction.ASC, "ruLiZhiShiJian")
        );
        return mongoTemplate.aggregate(aggregation, DriverDepartDto.class);
    }

    @Override
    public WagesResp getWagesList(WagesReq wagesReq) {
        WagesResp wagesResp = new WagesResp();
        Criteria criteria = new Criteria();
        //过滤条件
        criteria.and("cheDui").is(wagesReq.getCheDui());
        criteria.and("gongZiShiJian").is(wagesReq.getMonth());
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat monthFormat = new SimpleDateFormat("M");
        SimpleDateFormat yearFormat = new SimpleDateFormat("yyyy");
        Date parse = null;
        try {
            parse = format.parse(wagesReq.getMonth());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        String year = yearFormat.format(parse);
        String month = monthFormat.format(parse);
        //筛选时间范围
        TypedAggregation<WagesVo> aggregation = Aggregation.newAggregation(WagesVo.class,
                Aggregation.match(criteria),
                Aggregation.group("shenFenLeiBie").count().as("shenFenLeiBie")
                        .sum("shangGangGongZi").as("shangGangGongZi")
                        .sum("zhuJiaGangWeiBuTie").as("zhuJiaGangWeiBuTie")
                        .sum("yueDuKaoHe").as("yueDuKaoHe")
                        .sum("shangGangBuTie").as("shangGangBuTie")
                        .sum("gaoWenBuTie").as("gaoWenBuTie")
                        .sum("jiDuKaoHe").as("jiDuKaoHe")
                        .sum("yingFaXiaoJi").as("yingFaXiaoJi")
                        .sum("yangLaoBaoXian").as("yangLaoBaoXian")
                        .sum("yiLiaoBaoXian").as("yiLiaoBaoXian")
                        .sum("shiYeBaoXian").as("shiYeBaoXian")
                        .sum("geShui").as("geShui")
                        .sum("kaoHe").as("kaoHe")
                        .sum("shiFaJinE").as("shiFaJinE"),
                Aggregation.project("shenFenLeiBie", "shangGangGongZi", "zhuJiaGangWeiBuTie",
                                "yueDuKaoHe", "shangGangBuTie", "gaoWenBuTie", "jiDuKaoHe", "yingFaXiaoJi", "yangLaoBaoXian",
                                "yiLiaoBaoXian", "shiYeBaoXian", "geShui", "kaoHe", "shiFaJinE").and("shenFenLeiBie")
                        .previousOperation()
        );
        AggregationResults<WagesVo> aggregate = mongoTemplate.aggregate(aggregation, WagesVo.class);
        TypedAggregation<WagesVo> heJi = Aggregation.newAggregation(WagesVo.class,
                Aggregation.match(criteria),
                Aggregation.group().count().as("shenFenLeiBie")
                        .sum("shangGangGongZi").as("shangGangGongZi")
                        .sum("zhuJiaGangWeiBuTie").as("zhuJiaGangWeiBuTie")
                        .sum("yueDuKaoHe").as("yueDuKaoHe")
                        .sum("shangGangBuTie").as("shangGangBuTie")
                        .sum("gaoWenBuTie").as("gaoWenBuTie")
                        .sum("jiDuKaoHe").as("jiDuKaoHe")
                        .sum("yingFaXiaoJi").as("yingFaXiaoJi")
                        .sum("yangLaoBaoXian").as("yangLaoBaoXian")
                        .sum("yiLiaoBaoXian").as("yiLiaoBaoXian")
                        .sum("shiYeBaoXian").as("shiYeBaoXian")
                        .sum("geShui").as("geShui")
                        .sum("kaoHe").as("kaoHe")
                        .sum("shiFaJinE").as("shiFaJinE"),
                Aggregation.project("shenFenLeiBie", "shangGangGongZi", "zhuJiaGangWeiBuTie",
                                "yueDuKaoHe", "shangGangBuTie", "gaoWenBuTie", "jiDuKaoHe", "yingFaXiaoJi", "yangLaoBaoXian",
                                "yiLiaoBaoXian", "shiYeBaoXian", "geShui", "kaoHe", "shiFaJinE").and("shenFenLeiBie")
                        .previousOperation()
        );
        AggregationResults<WagesVo> heji = mongoTemplate.aggregate(heJi, WagesVo.class);
        WagesVo wagesVo = heji.getMappedResults().get(0);
        wagesVo.setShenFenLeiBie("合计");
        List<WagesVo> resp = new ArrayList<>();
        List<WagesVo> mappedResults = aggregate.getMappedResults();
        resp.addAll(mappedResults);
        resp.add(wagesVo);
        resp.forEach(this::getHeJi);
        wagesResp.setWagesVos(resp);
        wagesResp.setMonth(month);
        wagesResp.setYear(year);
        WagesResp wagesVos = mongoTemplate.findOne(CreateQuery.createCriteria("cheDuiid", wagesReq.getCheDui()), WagesResp.class);
        List<String> list = Arrays.asList(wagesVos.getCheDuiMingChen().split("/"));
        String replace = list.get(list.size() - 1).replace(" ", "");
        wagesResp.setCheDuiMingChen(replace);
        return wagesResp;
    }

    public static String getNum(String str) {
        BigDecimal decimal = new BigDecimal(str);
        DecimalFormat format = new DecimalFormat("###,###.00");
        String format1 = format.format(decimal);
        if (format1.equals(".00")) {
            return "0.00";
        }
        return format.format(decimal);
    }

    public void getHeJi(WagesVo a) {
        a.setShangGangGongZi(getNum(a.getShangGangGongZi()));
        a.setZhuJiaGangWeiBuTie(getNum(a.getZhuJiaGangWeiBuTie()));
        a.setYueDuKaoHe(getNum(a.getYueDuKaoHe()));
        a.setShangGangBuTie(getNum(a.getShangGangBuTie()));
        a.setGaoWenBuTie(getNum(a.getGaoWenBuTie()));
        a.setJiDuKaoHe(getNum(a.getJiDuKaoHe()));
        a.setYingFaXiaoJi(getNum(a.getYingFaXiaoJi()));
        a.setYangLaoBaoXian(getNum(a.getYangLaoBaoXian()));
        a.setYiLiaoBaoXian(getNum(a.getYiLiaoBaoXian()));
        a.setShiYeBaoXian(getNum(a.getShiYeBaoXian()));
        a.setGeShui(getNum(a.getGeShui()));
        a.setKaoHe(getNum(a.getKaoHe()));
        a.setShiFaJinE(getNum(a.getShiFaJinE()));
    }

    //获取月份
    public static List<String> getDateList() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        return DateUtils.getMonthList(DateUtils.getFirstYearDate(format.format(new Date())), new Date());
    }

    public static List<?> coverList(List list1, Class<?> c) {
        try {
            List<Object> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list1) && list1.size() > 0) {
                for (Object o : list1) {
                    Object o1 = c.newInstance();
                    BeanUtils.copyProperties(o, o1);
                    result.add(o1);
                }
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static List<?> mergeList(List list1, List list2, Class<?> c) {
        try {
            List<Object> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list1)) {
                for (Object o : list1) {
                    Object o1 = c.newInstance();
                    BeanUtils.copyProperties(o, o1);
                    result.add(o1);
                }
            }
            if (CollectionUtils.isNotEmpty(list2)) {
                for (Object o : list2) {
                    Object o1 = c.newInstance();
                    BeanUtils.copyProperties(o, o1);
                    result.add(o1);
                }
            }
            return result;
        } catch (InstantiationException e) {
            throw new RuntimeException(e);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
