package com.kan.modules.datav.service;

import com.kan.modules.datav.dto.*;
import com.kan.modules.datav.dto.req.DataReq;
import com.kan.modules.datav.dto.req.WagesReq;
import com.kan.modules.datav.dto.resp.DriverDepartResp;
import com.kan.modules.datav.dto.resp.InspectionResp;
import com.kan.modules.datav.dto.resp.SubListResp;
import com.kan.modules.datav.dto.resp.SummaryResp;

import java.util.List;

public interface DataBoardService {

    List<SummaryResp> getSummary(DataReq dataReq);


    DriverDepartResp getCarYear(DataReq dataReq);


    DriverDepartResp getDriverYear(DataReq dataReq);

    DriverDepartResp getComplaint(DataReq dataReq);

    DriverDepartResp getMoney();

//    DriverDepartResp getHidden(DataReq dataReq);

    DriverDepartResp getAccident(DataReq dataReq);

    List<SummaryResp> getHonor(DataReq dataReq);

    WagesResp getWagesList(WagesReq wagesReq);

    List<HiddenDtoNew> getHiddenNew(DataReq dataReq);

    List<SummaryResp> getDriverAge(DataReq dataReq);

    InspectionResp monthInspection(DataReq dataReq);

    DriverDepartResp onLine(DataReq dataReq);

    List<CarTypeNumDto> carType(DataReq dataReq);

    DriverDepartResp getChuZuPeople();

    Boolean saveSubscribe(SubscribeReq subscribeReq);
}
