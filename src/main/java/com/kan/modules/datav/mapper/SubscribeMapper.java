package com.kan.modules.datav.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kan.modules.datav.dto.CarYearDto;
import com.kan.modules.datav.dto.SubscribeDto;
import com.kan.modules.datav.dto.SubscribeReq;
import com.kan.modules.datav.dto.resp.DriverDepartResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SubscribeMapper extends BaseMapper<SubscribeReq> {

    List<SubscribeDto> getListByDate(String dept);

    List<SubscribeDto> getListAll();
}
