package com.kan;

import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;

@Data
@Document(collection  = "taix_payroll")
public class WagesVo {
    public String shenFenLeiBie;
    public BigDecimal shangGangGongZi;
    public BigDecimal zhuJiaGangWeiBuTie;
    public BigDecimal yueDuKaoHe;
    public BigDecimal shangGangBuTie;
    public BigDecimal gaoWenBuTie;
    public BigDecimal jiDuKaoHe;
    public BigDecimal yingFaXiaoJi;
    public BigDecimal yangLaoBaoXian;
    public BigDecimal yiLiaoBaoXian;
    public BigDecimal shiYeBaoXian;
    public BigDecimal geShui;
    public BigDecimal kaoHe;
    public BigDecimal shiFaJinE;
    public BigDecimal heJi;
}
