package com.kan.service;

import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;


@Document(collection = "iot_sequence")
public class SequenceId  implements Serializable {

	private static final long serialVersionUID = 7523059264202861246L;

	@Field
	@Indexed
	private String collName;// 集合名称

	@Field
	private long seqId;// 序列值

	public String getCollName() {
		return collName;
	}

	public void setCollName(String collName) {
		this.collName = collName;
	}

	public long getSeqId() {
		return seqId;
	}

	public void setSeqId(long seqId) {
		this.seqId = seqId;
	}


}
