package com.kan.service;

import com.baomidou.mybatisplus.annotation.TableField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.query.Criteria;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class MongoQueryUtil {
    /* 待条件的查询表达式
     *
     * @return
     */
    public static AggregationOperation getConditionalStage(boolean condition, Criteria criteria) {
        if (condition) {
            return Aggregation.match(criteria);
        }
        return Aggregation.match(new Criteria());
    }

    /**
     * 查询条件 支持等于/包含
     *
     * @param fieldName
     * @param value
     * @return
     */
    public static AggregationOperation getConditionalStage(String fieldName, Object value) {
        if (value != null) {
            if (value instanceof Collection) {
                if (CollectionUtils.isNotEmpty((Collection) value)) {
                    return Aggregation.match(Criteria.where(fieldName).in((List) value));
                }
                return Aggregation.match(new Criteria());
            }
            if (value instanceof String) {
                String temp = (String) value;
                if (StringUtils.isEmpty(temp)) {
                    return Aggregation.match(new Criteria());
                } else if (temp.contains(",")) {
                    //兼容为数字的情况
                    String[] strValues = temp.split(",");
                    //转为Long
                    List<Long> longValues = null;
                    try {
                        longValues = Arrays.stream(strValues).map(Long::parseLong).collect(Collectors.toList());
                    } catch (Exception e){

                    }
                    if(null != longValues){
                        return Aggregation.match(new Criteria().orOperator(Criteria.where(fieldName).in(strValues),Criteria.where(fieldName).in(longValues)));
                    }
                    return Aggregation.match(Criteria.where(fieldName).in(strValues));
                }else if (isNumber(temp)){
                    return Aggregation.match(new Criteria().orOperator(Criteria.where(fieldName).is(value),Criteria.where(fieldName).is(Long.valueOf(temp))));
                }
            }
            return Aggregation.match(Criteria.where(fieldName).is(value));
        }
        return Aggregation.match(new Criteria());
    }

    /**
     * 带条件表达式的查询条件 支持等于/包含
     *
     * @param fieldName
     * @param value
     * @return
     */
    public static AggregationOperation getConditionalStage(boolean condition, String fieldName, Object value) {
        if (condition) {
            return getConditionalStage(fieldName, value);
        }
        return Aggregation.match(new Criteria());
    }

    /**
     * 条件查询 支持like
     *
     * @param fieldName
     * @param value
     * @return
     */
    public static AggregationOperation getConditionalStageLike(String fieldName, Object value) {
        if (value != null && String.valueOf(value).length() > 0) {
            Pattern pattern = Pattern.compile("^.*" + value + ".*$", Pattern.CASE_INSENSITIVE);
            return Aggregation.match(Criteria.where(fieldName).regex(pattern));
        }
        return Aggregation.match(new Criteria());
    }

    /**
     * 条件查询 支持多个like,并用or连接
     *
     * @return
     */
    public static AggregationOperation getConditionalStageOrLike(Object value, String... fieldNames) {
        if (value == null || String.valueOf(value).length() == 0) {
            return Aggregation.match(new Criteria());
        }
        Pattern pattern = Pattern.compile("^.*" + value + ".*$", Pattern.CASE_INSENSITIVE);
        List<Criteria> criteriaList = Arrays.stream(fieldNames)
                .map(fieldName -> {
                    if (value != null) {
                        return Criteria.where(fieldName).regex(pattern);
                    }
                    return new Criteria();
                })
                .filter(criteria -> !criteria.getCriteriaObject().isEmpty()) // 过滤掉空的Criteria
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(criteriaList)) {
            return Aggregation.match(new Criteria().orOperator(criteriaList.toArray(new Criteria[0])));
        }
        return Aggregation.match(new Criteria()); // 返回空的匹配条件，不应用任何过滤
    }

    /**
     * 条件查询 支持in,并用or连接
     *
     * @return
     */
    public static AggregationOperation getConditionalStageForOr(List orgIds, String... fieldNames) {
        List<Criteria> criteriaList = Arrays.stream(fieldNames)
                .map(fieldName -> {
                    if (orgIds != null && !orgIds.isEmpty()) {
                        return Criteria.where(fieldName).in(orgIds);
                    }
                    return new Criteria();
                })
                .filter(criteria -> !criteria.getCriteriaObject().isEmpty()) // 过滤掉空的Criteria
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(criteriaList)) {
            return Aggregation.match(new Criteria().orOperator(criteriaList.toArray(new Criteria[0])));
        }
        return Aggregation.match(new Criteria()); // 返回空的匹配条件，不应用任何过滤
    }

    public static AggregationOperation createAggregationOperation(String fieldName, Object fieldValue) {
        if (fieldValue instanceof String && ((String) fieldValue).isEmpty()) {
            return Aggregation.match(new Criteria());
        } else if (fieldValue == null) {
            return Aggregation.match(new Criteria());
        }
        // 根据字段名和值创建对应的聚合操作
        return getConditionalStage(fieldName, fieldValue);
    }

    public static Aggregation buildAggregationFromRequest(Object request, List<AggregationOperation> others) {
        List<AggregationOperation> operations = new ArrayList<>();
        try {
            Field[] fields = request.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object fieldValue = field.get(request);
                TableField tableFieldAnnotation = field.getAnnotation(TableField.class);
                if ("serialVersionUID".equals(field.getName()) || null == fieldValue || (tableFieldAnnotation != null && !tableFieldAnnotation.exist())) {
                    continue;
                }
                AggregationOperation operation = createAggregationOperation(field.getName(), fieldValue);

                // 如果操作不为空，则添加到操作列表中
                if (operation != null) {
                    operations.add(operation);
                }
            }
            if (CollectionUtils.isNotEmpty(others)) {
                operations.addAll(others);
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("Failed to access fields in " + request.getClass() + " class", e);
        }
        return Aggregation.newAggregation(operations);
    }

    public static List mergeList(List list1, List list2) {
        if (null == list1) {
            list1 = new ArrayList<>();
        }
        List result = new ArrayList(list1);
        if (CollectionUtils.isNotEmpty(list2)) {
            result.addAll(list2);
        }
        return result;
    }

    public static boolean isNumber(String str) {
        // 正则表达式匹配整数或浮点数
        String regex = "^[0-9]*$";
        return str.matches(regex);
    }
}
