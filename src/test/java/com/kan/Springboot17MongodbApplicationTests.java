package com.kan;

import com.alibaba.fastjson.JSONObject;
import com.kan.common.utils.PlatHttpUtil;
import com.kan.modules.datav.dto.CarYearDto;
import com.kan.modules.datav.dto.req.DataReq;
import com.kan.modules.datav.dto.resp.DriverDepartResp;
import com.kan.modules.datav.dto.resp.SummaryResp;
import com.kan.modules.datav.service.DataBoardService;
import com.kan.modules.sys.entity.car_info;
import com.mongodb.BasicDBObject;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import static com.kan.service.MongoQueryUtil.getConditionalStage;

@SpringBootTest
class Springboot17MongodbApplicationTests {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    DataBoardService dataBoardService;
    @Test
    void contextLoads() {
        car_info book = new car_info();
        mongoTemplate.save(book);
    }
    @Test
    void find(){
        Criteria criteria=new Criteria();
        Criteria regex = Criteria.where("cheLiangLeiXing").regex("油车");
        Criteria id = Criteria.where("faDongJiHao").regex("4");
        criteria.andOperator(regex,id);
        Query query=new Query();
        query.addCriteria(criteria);
        List<car_info> books = mongoTemplate.find(query, car_info.class);
        System.out.println(books);
    }

    @Test
    //获取离职人员
    public void getWagesList(){
        Criteria criteria = new Criteria();
        //过滤条件
        criteria.and("cheDui").is("71c70ee809f40cef46c3b0d54fcc6d78");
        criteria.and("gongZiShiJian").is("2024-07");
        //筛选时间范围
        TypedAggregation<WagesVo> aggregation = Aggregation.newAggregation(WagesVo.class,
                Aggregation.match(criteria),
                Aggregation.group("shenFenLeiBie").count().as("shenFenLeiBie")
                        .sum("shangGangGongZi").as("shangGangGongZi")
                        .sum("zhuJiaGangWeiBuTie").as("zhuJiaGangWeiBuTie")
                        .sum("yueDuKaoHe").as("yueDuKaoHe")
                        .sum("shangGangBuTie").as("shangGangBuTie")
                        .sum("gaoWenBuTie").as("gaoWenBuTie")
                        .sum("jiDuKaoHe").as("jiDuKaoHe")
                        .sum("yingFaXiaoJi").as("yingFaXiaoJi")
                        .sum("yangLaoBaoXian").as("yangLaoBaoXian")
                        .sum("yiLiaoBaoXian").as("yiLiaoBaoXian")
                        .sum("shiYeBaoXian").as("shiYeBaoXian")
                        .sum("geShui").as("geShui")
                        .sum("kaoHe").as("kaoHe")
                        .sum("shiFaJinE").as("shiFaJinE"),
                Aggregation.project("shenFenLeiBie", "shangGangGongZi", "zhuJiaGangWeiBuTie",
                        "yueDuKaoHe", "shangGangBuTie", "gaoWenBuTie", "jiDuKaoHe", "yingFaXiaoJi", "yangLaoBaoXian",
                        "yiLiaoBaoXian", "shiYeBaoXian", "geShui", "kaoHe", "shiFaJinE").and("shenFenLeiBie")
                        .previousOperation()
        );
        AggregationResults<WagesVo> aggregate = mongoTemplate.aggregate(aggregation, WagesVo.class);
        List<WagesVo> mappedResults = aggregate.getMappedResults();
        mappedResults.forEach(a->{
            BigDecimal add = a.getShangGangGongZi().add(a.getZhuJiaGangWeiBuTie()).add(a.getYueDuKaoHe()).add(a.getShangGangBuTie())
                    .add(a.getGaoWenBuTie()).add(a.getJiDuKaoHe()).add(a.getYingFaXiaoJi()).add(a.getYangLaoBaoXian())
                    .add(a.getYiLiaoBaoXian()).add(a.getShiYeBaoXian()).add(a.getGeShui()).add(a.getKaoHe()).add(a.getShiFaJinE());
            a.setHeJi(add);
            System.out.println(a);
        });
    }
    @Test
    public void  syncTranEdu (){
        Date now = new Date();
        SimpleDateFormat format=new SimpleDateFormat("yyyy-MM-dd");
        String format1 = format.format(now);
        List<String> staticStatusList = new ArrayList<>();
        staticStatusList.add("1");
        staticStatusList.add("2");
        staticStatusList.add("4");
        Pattern pattern = Pattern.compile("^.*"+format1+".*$", Pattern.CASE_INSENSITIVE);
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(Criteria.where("isValid").is(1).and("completeTime").regex(pattern)),
                Aggregation.match(Criteria.where("isValid").is("1")),
                getConditionalStage("statisticalGroup", "0"),
                getConditionalStage("staticStatus", staticStatusList));
        List<UserLessonRecordSafeTrainUserVO> mappedResults = mongoTemplate.aggregate(aggregation, "_tableSafeTrainUserLesson", UserLessonRecordSafeTrainUserVO.class).getMappedResults();
        List<UserLessonRecordSafeTrainUserVO> all = mongoTemplate.findAll(UserLessonRecordSafeTrainUserVO.class);
        String encrypt = null;
        try {
            encrypt = SM4.encrypt(JSONObject.toJSONString(mappedResults), SM4.KEY);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String s = PlatHttpUtil.sendPostParams("http://localhost:30001/api/training/saveTrain",JSONObject.toJSONString(encrypt),null,
                "UTF-8",null);
        System.out.println(s);
    }
}
