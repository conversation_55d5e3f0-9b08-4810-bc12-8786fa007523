package com.kan.modules.oil;

import com.alibaba.fastjson.JSON;
import com.kan.modules.oil.dto.request.OilReservationSaveReqDTO;
import com.kan.modules.oil.service.OilReservationService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 油品提取预约接口测试
 */
@Slf4j
@SpringBootTest
public class OilReservationControllerTest {

    @Autowired
    private OilReservationService oilReservationService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Test
    public void testSaveOilReservation() {
        try {
            // 创建测试数据
            OilReservationSaveReqDTO reqDTO = new OilReservationSaveReqDTO();
            
            // 设置基本信息
            reqDTO.setScheduledTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000)); // 明天
            reqDTO.setOilDepot("北京石油库");
            reqDTO.setOilType("92#");
            reqDTO.setOilTonnage(new BigDecimal("30.00"));
            reqDTO.setLicensePlate("京A12345");
            reqDTO.setDriverName("张三");
            reqDTO.setDriverIdCard("110101199001011234");
            reqDTO.setDriverPhone("13800138000");
            reqDTO.setDriverLicenseNumber("110101199001011234");
            reqDTO.setLicenseArchiveNumber("A123456789");
            reqDTO.setTankCount("双仓");
            reqDTO.setRemark("测试预约");
            
            // 设置各仓装载信息（格式：仓号:装载量:油品类型;仓号:装载量:油品类型）
            reqDTO.setTankLoadInfo("1:15.00:92#;2:15.00:92#");
            
            log.info("测试数据: {}", JSON.toJSONString(reqDTO, true));
            
            // 调用保存方法
            String reservationId = oilReservationService.save(reqDTO);
            
            log.info("保存成功，预约ID: {}", reservationId);
            
            // 验证保存结果
            assert reservationId != null && !reservationId.isEmpty();
            
            // 查询保存的数据
            var savedData = oilReservationService.findById(reservationId);
            log.info("查询结果: {}", JSON.toJSONString(savedData, true));
            
            assert savedData != null;
            assert savedData.getDriverName().equals("张三");
            assert savedData.getApprovalStatus() == 0; // 待审批
            
            log.info("✅ 油品提取预约接口测试通过");
            
        } catch (Exception e) {
            log.error("❌ 油品提取预约接口测试失败", e);
            throw e;
        }
    }

    @Test
    public void testMongoConnection() {
        try {
            // 测试MongoDB连接
            String dbName = mongoTemplate.getDb().getName();
            log.info("MongoDB连接正常，数据库名称: {}", dbName);
            
            // 测试集合操作
            boolean collectionExists = mongoTemplate.collectionExists("oil_reservation");
            log.info("oil_reservation集合存在: {}", collectionExists);
            
            log.info("✅ MongoDB连接测试通过");
            
        } catch (Exception e) {
            log.error("❌ MongoDB连接测试失败", e);
            throw e;
        }
    }

    @Test
    public void testValidation() {
        try {
            // 测试数据验证
            OilReservationSaveReqDTO reqDTO = new OilReservationSaveReqDTO();
            
            // 故意设置无效数据
            reqDTO.setScheduledTime(new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000)); // 昨天（过去时间）
            reqDTO.setOilType("98#"); // 无效油品类型
            reqDTO.setOilTonnage(new BigDecimal("30.00"));
            reqDTO.setTankCount("双仓");
            
            // 设置各仓装载信息（总和不等于提油吨数）
            reqDTO.setTankLoadInfo("1:10.00:92#;2:10.00:92#"); // 总和只有20，不等于30
            
            try {
                oilReservationService.save(reqDTO);
                log.error("❌ 验证测试失败：应该抛出异常但没有抛出");
                assert false;
            } catch (Exception e) {
                log.info("✅ 验证测试通过：正确抛出异常 - {}", e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("❌ 验证测试失败", e);
            throw e;
        }
    }
}
