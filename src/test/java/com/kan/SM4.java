/*
 *
 *  * 文件名：SM4.java
 *  * Copyright © 2011-2020 重庆若谷信息技术有限公司 版权所有
 *
 */

package com.kan;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.SecureRandom;
import java.security.Security;

/**
 *
 * 国密加密 <br>
 * 加密算法：SM4 <br>
 * 加密模式：ECB <br>
 * 密钥长度：128 <br>
 * 填充方式：PKCS5Padding <br>
 *
 * <AUTHOR>
 * @since 1.0
 */
public class SM4 {

    private static final BouncyCastleProvider BOUNCY_CASTLE_PROVIDER = new BouncyCastleProvider();
    private static final String BOUNCY_CASTLE_PROVIDER_NME = BouncyCastleProvider.PROVIDER_NAME;
    private static final String SM4_ALGORITHM = "SM4";
    public static String KEY = "71698e2da427cf01a77ed0c451d77c02";
    /**
     * 密钥长度
     */
    private static final int DEFAULT_KEY_SIZE = 128;
    private static final String DEFAULT_ENCODING = "UTF-8";

    static {
        Security.removeProvider(BOUNCY_CASTLE_PROVIDER_NME);
        Security.addProvider(BOUNCY_CASTLE_PROVIDER);
    }

    /**
     * SM4 加密
     *
     * @param plainText      明文
     * @param key            密钥
     * @param modeAndPadding 加密方式
     * @param iv             初始向量(ECB模式下传NULL)
     * @return
     * @throws Exception
     */
    private static byte[] encrypt(byte[] plainText, byte[] key, SM4ModeAndPaddingEnum modeAndPadding, byte[] iv) throws Exception {
        return sm4(plainText, key, modeAndPadding, iv, Cipher.ENCRYPT_MODE);
    }

    /**
     * SM4 解密
     *
     * @param cipherText            密文
     * @param key                   密钥
     * @param sm4ModeAndPaddingEnum 加密模式和padding模式
     * @param iv                    初始向量(ECB模式下传NULL)
     * @return
     * @throws Exception
     */
    private static byte[] decrypt(byte[] cipherText, byte[] key, SM4ModeAndPaddingEnum sm4ModeAndPaddingEnum, byte[] iv) throws Exception {
        return sm4(cipherText, key, sm4ModeAndPaddingEnum, iv, Cipher.DECRYPT_MODE);
    }

    /**
     * SM4算法
     *
     * @param input                 输入数据
     * @param key                   密钥
     * @param sm4ModeAndPaddingEnum SM4模式
     * @param iv                    初始向量ECB模式下为null
     * @param mode                  加密或解密模式
     * @return
     * @throws Exception
     */
    private static byte[] sm4(byte[] input, byte[] key, SM4ModeAndPaddingEnum sm4ModeAndPaddingEnum, byte[] iv, int mode) throws Exception {
        SecretKeySpec sm4Key = new SecretKeySpec(key, SM4_ALGORITHM);
        Cipher cipher = Cipher.getInstance(sm4ModeAndPaddingEnum.getName(), BOUNCY_CASTLE_PROVIDER_NME);
        if (iv == null) {
            cipher.init(mode, sm4Key);
        } else {
            cipher.init(mode, sm4Key, new IvParameterSpec(iv));
        }
        return cipher.doFinal(input);
    }

    private enum SM4ModeAndPaddingEnum {
        SM4_ECB_NoPadding("SM4/ECB/NoPadding"),
        SM4_ECB_PKCS5Padding("SM4/ECB/PKCS5Padding"),
        SM4_ECB_PKCS7Padding("SM4/ECB/PKCS7Padding");

        private String name;

        SM4ModeAndPaddingEnum(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 生成密钥
     *
     * @return
     * @throws Exception
     */
    public static byte[] generateKey() throws NoSuchProviderException, NoSuchAlgorithmException {
        KeyGenerator kg = KeyGenerator.getInstance(SM4_ALGORITHM, BOUNCY_CASTLE_PROVIDER_NME);
        kg.init(DEFAULT_KEY_SIZE, new SecureRandom());
        return kg.generateKey().getEncoded();
    }

    /**
     * 加密
     *
     * @param plainText
     * @param hexKey
     * @return
     */
    public static String encrypt(String plainText, String hexKey) throws Exception {
        byte[] plain = plainText.getBytes(DEFAULT_ENCODING);
        byte[] key = ByteUtils.fromHexString(hexKey);
        return ByteUtils.toHexString(encrypt(plain, key, SM4ModeAndPaddingEnum.SM4_ECB_PKCS5Padding, null));
    }

    /**
     * 解密
     *
     * @param cipherString
     * @return
     */
    public static String decrypt(String cipherString, String base64Key) throws Exception{
        byte[] cipher = ByteUtils.fromHexString(cipherString);
        byte[] key = ByteUtils.fromHexString(base64Key);
        return new String(decrypt(cipher, key, SM4ModeAndPaddingEnum.SM4_ECB_PKCS5Padding, null), DEFAULT_ENCODING);
    }

    public static void main(String[] args) throws Exception {

        String hexKey = ByteUtils.toHexString(SM4.generateKey());
        hexKey = "FZKJFCD7CE258D915F1A";
        System.out.println(String.format("密钥：%s", hexKey));
        String plainText = "[{" +
                "\"idno\": \"test-idno-0\"," +
                "\"name\": \"test-name-0\"," +
                "\"month\": \"2021-02\"," +
                "\"courseStat\": 1," +
                "\"learnStat\": 1," +
                "\"examineStat\": 1," +
                "\"score\": 0," +
                "\"duration\": 901," +
                "\"compeleteTime\": \"2021-01-01 08:10:10\"," +
                "\"compCode\": \"test-compCode-0\"," +
                "\"compName\": \"test-compName-0\"" +
                "}]";
        System.out.println(String.format("文本：%s", plainText));
        String a = SM4.encrypt(plainText, hexKey);
        System.out.println(String.format("加密结果：%s", a));
        String b = SM4.decrypt(a, hexKey);
        System.out.println(String.format("解密结果：%s", b));
    }
}
